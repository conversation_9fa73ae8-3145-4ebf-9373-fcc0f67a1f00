/*! @name pkcs7 @version 1.0.4 @license Apache-2.0 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).pkcs7={})}(this,function(e){"use strict";var t;t=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]];e.VERSION="1.0.4",e.pad=function(e){var n=t[e.byteLength%16||0],r=new Uint8Array(e.byteLength+n.length);return r.set(e),r.set(n,e.byteLength),r},e.unpad=function(e){return e.subarray(0,e.byteLength-e[e.byteLength-1])},Object.defineProperty(e,"__esModule",{value:!0})});
