/*! @name aes-decrypter @version 4.0.2 @license Apache-2.0 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).aesDecrypter={})}(this,(function(t){"use strict";let e=null;class s{constructor(t){let s,i,n;e||(e=function(){const t=[[[],[],[],[],[]],[[],[],[],[],[]]],e=t[0],s=t[1],i=e[4],n=s[4];let r,o,l;const c=[],h=[];let u,f,a,y,p,b;for(r=0;r<256;r++)h[(c[r]=r<<1^283*(r>>7))^r]=r;for(o=l=0;!i[o];o^=u||1,l=h[l]||1)for(y=l^l<<1^l<<2^l<<3^l<<4,y=y>>8^255&y^99,i[o]=y,n[y]=o,a=c[f=c[u=c[o]]],b=16843009*a^65537*f^257*u^16843008*o,p=257*c[y]^16843008*y,r=0;r<4;r++)e[r][o]=p=p<<24^p>>>8,s[r][y]=b=b<<24^b>>>8;for(r=0;r<5;r++)e[r]=e[r].slice(0),s[r]=s[r].slice(0);return t}()),this._tables=[[e[0][0].slice(),e[0][1].slice(),e[0][2].slice(),e[0][3].slice(),e[0][4].slice()],[e[1][0].slice(),e[1][1].slice(),e[1][2].slice(),e[1][3].slice(),e[1][4].slice()]];const r=this._tables[0][4],o=this._tables[1],l=t.length;let c=1;if(4!==l&&6!==l&&8!==l)throw new Error("Invalid aes key size");const h=t.slice(0),u=[];for(this._key=[h,u],s=l;s<4*l+28;s++)n=h[s-1],(s%l==0||8===l&&s%l==4)&&(n=r[n>>>24]<<24^r[n>>16&255]<<16^r[n>>8&255]<<8^r[255&n],s%l==0&&(n=n<<8^n>>>24^c<<24,c=c<<1^283*(c>>7))),h[s]=h[s-l]^n;for(i=0;s;i++,s--)n=h[3&i?s:s-4],u[i]=s<=4||i<4?n:o[0][r[n>>>24]]^o[1][r[n>>16&255]]^o[2][r[n>>8&255]]^o[3][r[255&n]]}decrypt(t,e,s,i,n,r){const o=this._key[1];let l,c,h,u=t^o[0],f=i^o[1],a=s^o[2],y=e^o[3];const p=o.length/4-2;let b,d=4;const _=this._tables[1],g=_[0],m=_[1],w=_[2],v=_[3],A=_[4];for(b=0;b<p;b++)l=g[u>>>24]^m[f>>16&255]^w[a>>8&255]^v[255&y]^o[d],c=g[f>>>24]^m[a>>16&255]^w[y>>8&255]^v[255&u]^o[d+1],h=g[a>>>24]^m[y>>16&255]^w[u>>8&255]^v[255&f]^o[d+2],y=g[y>>>24]^m[u>>16&255]^w[f>>8&255]^v[255&a]^o[d+3],d+=4,u=l,f=c,a=h;for(b=0;b<4;b++)n[(3&-b)+r]=A[u>>>24]<<24^A[f>>16&255]<<16^A[a>>8&255]<<8^A[255&y]^o[d++],l=u,u=f,f=a,a=y,y=l}}var i=function(){function t(){this.listeners={}}var e=t.prototype;return e.on=function(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)},e.off=function(t,e){if(!this.listeners[t])return!1;var s=this.listeners[t].indexOf(e);return this.listeners[t]=this.listeners[t].slice(0),this.listeners[t].splice(s,1),s>-1},e.trigger=function(t){var e=this.listeners[t];if(e)if(2===arguments.length)for(var s=e.length,i=0;i<s;++i)e[i].call(this,arguments[1]);else for(var n=Array.prototype.slice.call(arguments,1),r=e.length,o=0;o<r;++o)e[o].apply(this,n)},e.dispose=function(){this.listeners={}},e.pipe=function(t){this.on("data",(function(e){t.push(e)}))},t}();class n extends i{constructor(){super(i),this.jobs=[],this.delay=1,this.timeout_=null}processJob_(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}push(t){this.jobs.push(t),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}
/*! @name pkcs7 @version 1.0.4 @license Apache-2.0 */const r=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},o=function(t,e,i){const n=new Int32Array(t.buffer,t.byteOffset,t.byteLength>>2),o=new s(Array.prototype.slice.call(e)),l=new Uint8Array(t.byteLength),c=new Int32Array(l.buffer);let h,u,f,a,y,p,b,d,_;for(h=i[0],u=i[1],f=i[2],a=i[3],_=0;_<n.length;_+=4)y=r(n[_]),p=r(n[_+1]),b=r(n[_+2]),d=r(n[_+3]),o.decrypt(y,p,b,d,c,_),c[_]=r(c[_]^h),c[_+1]=r(c[_+1]^u),c[_+2]=r(c[_+2]^f),c[_+3]=r(c[_+3]^a),h=y,u=p,f=b,a=d;return l};class l{constructor(t,e,s,i){const o=l.STEP,c=new Int32Array(t.buffer),h=new Uint8Array(t.byteLength);let u=0;for(this.asyncStream_=new n,this.asyncStream_.push(this.decryptChunk_(c.subarray(u,u+o),e,s,h)),u=o;u<c.length;u+=o)s=new Uint32Array([r(c[u-4]),r(c[u-3]),r(c[u-2]),r(c[u-1])]),this.asyncStream_.push(this.decryptChunk_(c.subarray(u,u+o),e,s,h));this.asyncStream_.push((function(){var t;i(null,(t=h).subarray(0,t.byteLength-t[t.byteLength-1]))}))}static get STEP(){return 32e3}decryptChunk_(t,e,s,i){return function(){const n=o(t,e,s);i.set(n,t.byteOffset)}}}t.AsyncStream=n,t.Decrypter=l,t.decrypt=o,Object.defineProperty(t,"__esModule",{value:!0})}));
