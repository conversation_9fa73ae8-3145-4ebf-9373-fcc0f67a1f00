'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useVideoStore } from '@/store/videoStore'
import { supabase } from '@/lib/supabase'
import { Search, Filter, Calendar, Clock, Eye, SortAsc } from 'lucide-react'

interface SearchFilters {
  duration: 'any' | 'short' | 'medium' | 'long'
  uploadDate: 'any' | 'hour' | 'today' | 'week' | 'month' | 'year'
  sortBy: 'relevance' | 'upload_date' | 'view_count' | 'rating'
  category: string
}

export default function SearchPage() {
  const searchParams = useSearchParams()
  const query = searchParams.get('q') || ''
  
  const [videos, setVideos] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    duration: 'any',
    uploadDate: 'any',
    sortBy: 'relevance',
    category: 'any'
  })

  const categories = [
    'any', 'music', 'gaming', 'sports', 'news', 'entertainment', 'education', 'technology', 'travel', 'cooking'
  ]

  const searchVideos = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    try {
      let supabaseQuery = supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_public', true)
        .or(`title.ilike.%${query}%, description.ilike.%${query}%`)

      // Apply category filter
      if (filters.category !== 'any') {
        supabaseQuery = supabaseQuery.eq('category', filters.category)
      }

      // Apply upload date filter
      if (filters.uploadDate !== 'any') {
        const now = new Date()
        let startDate: Date

        switch (filters.uploadDate) {
          case 'hour':
            startDate = new Date(now.getTime() - 60 * 60 * 1000)
            break
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            break
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            break
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1)
            break
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1)
            break
          default:
            startDate = new Date(0)
        }

        supabaseQuery = supabaseQuery.gte('created_at', startDate.toISOString())
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'upload_date':
          supabaseQuery = supabaseQuery.order('created_at', { ascending: false })
          break
        case 'view_count':
          supabaseQuery = supabaseQuery.order('views', { ascending: false })
          break
        case 'rating':
          supabaseQuery = supabaseQuery.order('likes', { ascending: false })
          break
        default: // relevance
          supabaseQuery = supabaseQuery.order('views', { ascending: false })
      }

      const { data, error } = await supabaseQuery.limit(50)

      if (error) throw error

      let results = data || []

      // Apply duration filter (client-side since we don't have duration in all videos)
      if (filters.duration !== 'any') {
        results = results.filter(video => {
          if (!video.duration) return true // Include videos without duration info
          
          switch (filters.duration) {
            case 'short':
              return video.duration <= 240 // 4 minutes
            case 'medium':
              return video.duration > 240 && video.duration <= 1200 // 4-20 minutes
            case 'long':
              return video.duration > 1200 // 20+ minutes
            default:
              return true
          }
        })
      }

      setVideos(results)
    } catch (error) {
      console.error('Error searching videos:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    searchVideos()
  }, [query, filters])

  const resetFilters = () => {
    setFilters({
      duration: 'any',
      uploadDate: 'any',
      sortBy: 'relevance',
      category: 'any'
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== 'any' && value !== 'relevance')

  if (!query) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Search className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Search for videos</h2>
          <p className="text-muted-foreground">
            Enter a search term to find videos on the platform.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">Search Results</h1>
            <p className="text-muted-foreground">
              {loading ? 'Searching...' : `${videos.length} results for "${query}"`}
            </p>
          </div>
          
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center"
          >
            <Filter className="w-4 h-4 mr-1" />
            Filters
            {hasActiveFilters && (
              <Badge variant="destructive" className="ml-2 px-1 py-0 text-xs">
                {Object.values(filters).filter(v => v !== 'any' && v !== 'relevance').length}
              </Badge>
            )}
          </Button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-card p-4 rounded-lg border mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Duration Filter */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  <Clock className="w-4 h-4 inline mr-1" />
                  Duration
                </label>
                <select
                  value={filters.duration}
                  onChange={(e) => setFilters(prev => ({ ...prev, duration: e.target.value as any }))}
                  className="w-full px-3 py-2 border rounded-md bg-background"
                >
                  <option value="any">Any duration</option>
                  <option value="short">Under 4 minutes</option>
                  <option value="medium">4-20 minutes</option>
                  <option value="long">Over 20 minutes</option>
                </select>
              </div>

              {/* Upload Date Filter */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  Upload Date
                </label>
                <select
                  value={filters.uploadDate}
                  onChange={(e) => setFilters(prev => ({ ...prev, uploadDate: e.target.value as any }))}
                  className="w-full px-3 py-2 border rounded-md bg-background"
                >
                  <option value="any">Any time</option>
                  <option value="hour">Last hour</option>
                  <option value="today">Today</option>
                  <option value="week">This week</option>
                  <option value="month">This month</option>
                  <option value="year">This year</option>
                </select>
              </div>

              {/* Sort By Filter */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  <SortAsc className="w-4 h-4 inline mr-1" />
                  Sort By
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
                  className="w-full px-3 py-2 border rounded-md bg-background"
                >
                  <option value="relevance">Relevance</option>
                  <option value="upload_date">Upload date</option>
                  <option value="view_count">View count</option>
                  <option value="rating">Rating</option>
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-md bg-background"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'any' ? 'Any category' : category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {hasActiveFilters && (
              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" size="sm" onClick={resetFilters}>
                  Clear all filters
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {loading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="aspect-video bg-muted rounded-lg mb-3" />
              <div className="flex space-x-3">
                <div className="w-9 h-9 bg-muted rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded mb-2" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : videos.length === 0 ? (
        <div className="text-center py-12">
          <Search className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No results found</h2>
          <p className="text-muted-foreground mb-4">
            Try different keywords or adjust your filters.
          </p>
          {hasActiveFilters && (
            <Button variant="outline" onClick={resetFilters}>
              Clear filters
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {videos.map((video) => (
            <VideoCard key={video.id} video={video} />
          ))}
        </div>
      )}
    </div>
  )
}
