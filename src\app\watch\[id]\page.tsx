'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { VideoPlayer } from '@/components/video/VideoPlayer'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { useVideoStore } from '@/store/videoStore'
import { useAuthStore } from '@/store/authStore'
import { supabase } from '@/lib/supabase'
import { formatDistanceToNow } from 'date-fns'
import { ThumbsUp, ThumbsDown, Share, Eye, MessageCircle, MoreHorizontal } from 'lucide-react'
import { SubscribeButton } from '@/components/video/SubscribeButton'
import { AddToPlaylistButton } from '@/components/video/AddToPlaylistButton'
import { WatchLaterButton } from '@/components/video/WatchLaterButton'
import { useHistoryStore } from '@/store/historyStore'

interface Comment {
  id: string
  content: string
  likes: number
  created_at: string
  profiles: {
    username: string
    avatar_url: string | null
  }
}

export default function WatchPage() {
  const params = useParams()
  const videoId = params.id as string

  const { currentVideo, videos, loading, fetchVideoById, fetchVideos, incrementViews } = useVideoStore()
  const { user } = useAuthStore()
  const { likeVideo, dislikeVideo, getVideoLikeStatus } = useHistoryStore()

  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [loadingComments, setLoadingComments] = useState(false)
  const [submittingComment, setSubmittingComment] = useState(false)

  useEffect(() => {
    if (videoId) {
      fetchVideoById(videoId)
      fetchVideos() // For related videos
      incrementViews(videoId)
      fetchComments()
    }
  }, [videoId, fetchVideoById, fetchVideos, incrementViews])

  const fetchComments = async () => {
    setLoadingComments(true)
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('video_id', videoId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setComments(data || [])
    } catch (error) {
      console.error('Error fetching comments:', error)
    } finally {
      setLoadingComments(false)
    }
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newComment.trim()) return

    setSubmittingComment(true)
    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          video_id: videoId,
          user_id: user.id,
          content: newComment.trim(),
        })

      if (error) throw error

      setNewComment('')
      fetchComments() // Refresh comments
    } catch (error) {
      console.error('Error submitting comment:', error)
    } finally {
      setSubmittingComment(false)
    }
  }

  const handleLike = async () => {
    if (currentVideo) {
      try {
        await likeVideo(currentVideo.id)
        // Refresh video data to get updated like count
        fetchVideoById(currentVideo.id)
      } catch (error) {
        console.error('Error liking video:', error)
      }
    }
  }

  const handleDislike = async () => {
    if (currentVideo) {
      try {
        await dislikeVideo(currentVideo.id)
        // Refresh video data to get updated dislike count
        fetchVideoById(currentVideo.id)
      } catch (error) {
        console.error('Error disliking video:', error)
      }
    }
  }

  const handleShare = async () => {
    if (currentVideo) {
      try {
        await navigator.share({
          title: currentVideo.title,
          text: currentVideo.description || '',
          url: window.location.href,
        })
      } catch (error) {
        // Fallback to copying to clipboard
        navigator.clipboard.writeText(window.location.href)
        alert('Link copied to clipboard!')
      }
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  if (loading || !currentVideo) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="aspect-video bg-muted rounded-lg mb-4" />
          <div className="h-6 bg-muted rounded mb-2" />
          <div className="h-4 bg-muted rounded w-2/3" />
        </div>
      </div>
    )
  }

  const relatedVideos = videos.filter(v => v.id !== currentVideo.id).slice(0, 10)

  return (
    <div className="flex flex-col lg:flex-row gap-6 p-6">
      {/* Main Content */}
      <div className="flex-1">
        {/* Video Player */}
        <div className="mb-4">
          <VideoPlayer
            src={currentVideo.video_url}
            poster={currentVideo.thumbnail_url || undefined}
            videoId={currentVideo.id}
          />
        </div>

        {/* Video Info */}
        <div className="mb-6">
          <h1 className="text-xl font-bold mb-2">{currentVideo.title}</h1>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {formatViews(currentVideo.views)} views
              </div>
              <span>•</span>
              <span>
                {formatDistanceToNow(new Date(currentVideo.created_at), { addSuffix: true })}
              </span>
            </div>

            <div className="flex items-center space-x-2 flex-wrap">
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLike}
                  className={getVideoLikeStatus(currentVideo.id) === 'liked' ? 'bg-blue-50 border-blue-200 text-blue-700' : ''}
                >
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  {currentVideo.likes}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDislike}
                  className={getVideoLikeStatus(currentVideo.id) === 'disliked' ? 'bg-red-50 border-red-200 text-red-700' : ''}
                >
                  <ThumbsDown className="w-4 h-4 mr-1" />
                  {currentVideo.dislikes}
                </Button>
              </div>

              <AddToPlaylistButton videoId={currentVideo.id} />
              <WatchLaterButton videoId={currentVideo.id} />

              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share className="w-4 h-4 mr-1" />
                Share
              </Button>

              <Button variant="outline" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Channel Info */}
          <div className="flex items-center space-x-3 p-4 bg-muted rounded-lg">
            <Avatar className="w-10 h-10">
              <AvatarImage src={(currentVideo as any).profiles?.avatar_url || ''} />
              <AvatarFallback>
                {(currentVideo as any).profiles?.username?.charAt(0).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold">
                {(currentVideo as any).profiles?.username || 'Unknown User'}
              </h3>
              <p className="text-sm text-muted-foreground">
                {(currentVideo as any).profiles?.full_name}
              </p>
            </div>
            <SubscribeButton
              channelId={currentVideo.user_id}
              channelName={(currentVideo as any).profiles?.username}
            />
          </div>

          {/* Description */}
          {currentVideo.description && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <p className="whitespace-pre-wrap">{currentVideo.description}</p>
            </div>
          )}
        </div>

        {/* Comments Section */}
        <div>
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Comments ({comments.length})
          </h2>

          {/* Add Comment */}
          {user && (
            <form onSubmit={handleSubmitComment} className="mb-6">
              <div className="flex space-x-3">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarImage src={user.user_metadata?.avatar_url} />
                  <AvatarFallback>
                    {user.user_metadata?.username?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="mb-2"
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setNewComment('')}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      size="sm"
                      disabled={!newComment.trim() || submittingComment}
                    >
                      {submittingComment ? 'Posting...' : 'Comment'}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          )}

          {/* Comments List */}
          <div className="space-y-4">
            {loadingComments ? (
              <div>Loading comments...</div>
            ) : comments.length === 0 ? (
              <p className="text-muted-foreground">No comments yet. Be the first to comment!</p>
            ) : (
              comments.map((comment) => (
                <div key={comment.id} className="flex space-x-3">
                  <Avatar className="w-8 h-8 flex-shrink-0">
                    <AvatarImage src={comment.profiles.avatar_url || ''} />
                    <AvatarFallback>
                      {comment.profiles.username.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-semibold text-sm">
                        {comment.profiles.username}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                      </span>
                    </div>
                    <p className="text-sm mb-2">{comment.content}</p>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="w-3 h-3 mr-1" />
                        {comment.likes}
                      </Button>
                      <Button variant="ghost" size="sm">
                        <ThumbsDown className="w-3 h-3" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        Reply
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Sidebar - Related Videos */}
      <div className="w-full lg:w-96">
        <h2 className="text-lg font-semibold mb-4">Related Videos</h2>
        <div className="space-y-4">
          {relatedVideos.map((video) => (
            <div key={video.id} className="flex space-x-3">
              <div className="w-40 flex-shrink-0">
                <VideoCard video={video} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
