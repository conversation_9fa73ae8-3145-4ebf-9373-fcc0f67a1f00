'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useSubscriptionStore } from '@/store/subscriptionStore'
import { useAuthStore } from '@/store/authStore'
import { Bell, BellOff } from 'lucide-react'

interface SubscribeButtonProps {
  channelId: string
  channelName?: string
  className?: string
}

export function SubscribeButton({ channelId, channelName, className }: SubscribeButtonProps) {
  const { user } = useAuthStore()
  const { subscribe, unsubscribe, isSubscribed, getSubscriberCount } = useSubscriptionStore()
  const [subscriberCount, setSubscriberCount] = useState(0)
  const [loading, setLoading] = useState(false)

  const subscribed = isSubscribed(channelId)

  useEffect(() => {
    const fetchSubscriberCount = async () => {
      const count = await getSubscriberCount(channelId)
      setSubscriberCount(count)
    }
    
    fetchSubscriberCount()
  }, [channelId, getSubscriberCount, subscribed])

  const handleSubscribe = async () => {
    if (!user) {
      // Could trigger auth modal here
      alert('Please sign in to subscribe to channels')
      return
    }

    setLoading(true)
    try {
      if (subscribed) {
        await unsubscribe(channelId)
      } else {
        await subscribe(channelId)
      }
    } catch (error) {
      console.error('Error toggling subscription:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatSubscriberCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Button
        onClick={handleSubscribe}
        disabled={loading || !user}
        variant={subscribed ? 'secondary' : 'default'}
        className={`flex items-center ${
          subscribed 
            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300' 
            : 'bg-red-600 text-white hover:bg-red-700'
        }`}
      >
        {subscribed ? (
          <>
            <BellOff className="w-4 h-4 mr-1" />
            Subscribed
          </>
        ) : (
          <>
            <Bell className="w-4 h-4 mr-1" />
            Subscribe
          </>
        )}
      </Button>
      
      {subscriberCount > 0 && (
        <span className="text-sm text-muted-foreground">
          {formatSubscriberCount(subscriberCount)} subscriber{subscriberCount !== 1 ? 's' : ''}
        </span>
      )}
    </div>
  )
}
