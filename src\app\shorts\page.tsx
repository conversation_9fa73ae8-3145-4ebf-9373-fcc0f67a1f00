'use client'

import { useEffect, useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SubscribeButton } from '@/components/video/SubscribeButton'
import { AddToPlaylistButton } from '@/components/video/AddToPlaylistButton'
import { supabase } from '@/lib/supabase'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  ThumbsUp, 
  ThumbsDown, 
  MessageCircle, 
  Share,
  MoreVertical,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import Link from 'next/link'

interface ShortVideo {
  id: string
  title: string
  description: string | null
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  views: number
  likes: number
  dislikes: number
  user_id: string
  created_at: string
  profiles: {
    username: string
    avatar_url: string | null
  }
}

export default function ShortsPage() {
  const [shorts, setShorts] = useState<ShortVideo[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])
  const containerRef = useRef<HTMLDivElement>(null)
  
  const { user } = useAuthStore()
  const { likeVideo, dislikeVideo, addToHistory } = useHistoryStore()

  useEffect(() => {
    fetchShorts()
  }, [])

  useEffect(() => {
    // Auto-play current video
    const currentVideo = videoRefs.current[currentIndex]
    if (currentVideo) {
      if (isPlaying) {
        currentVideo.play()
      } else {
        currentVideo.pause()
      }
    }

    // Pause other videos
    videoRefs.current.forEach((video, index) => {
      if (video && index !== currentIndex) {
        video.pause()
      }
    })
  }, [currentIndex, isPlaying])

  const fetchShorts = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_public', true)
        .lt('duration', 60) // Short videos under 60 seconds
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) throw error
      setShorts(data || [])
    } catch (error) {
      console.error('Error fetching shorts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleVideoClick = () => {
    setIsPlaying(!isPlaying)
  }

  const handleMuteToggle = () => {
    setIsMuted(!isMuted)
    const currentVideo = videoRefs.current[currentIndex]
    if (currentVideo) {
      currentVideo.muted = !isMuted
    }
  }

  const handleLike = async (videoId: string) => {
    if (user) {
      try {
        await likeVideo(videoId)
      } catch (error) {
        console.error('Error liking video:', error)
      }
    }
  }

  const handleDislike = async (videoId: string) => {
    if (user) {
      try {
        await dislikeVideo(videoId)
      } catch (error) {
        console.error('Error disliking video:', error)
      }
    }
  }

  const handleShare = async (video: ShortVideo) => {
    try {
      await navigator.share({
        title: video.title,
        text: video.description || '',
        url: `${window.location.origin}/shorts/${video.id}`,
      })
    } catch (error) {
      navigator.clipboard.writeText(`${window.location.origin}/shorts/${video.id}`)
      alert('Link copied to clipboard!')
    }
  }

  const navigateToNext = () => {
    if (currentIndex < shorts.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case ' ':
        e.preventDefault()
        setIsPlaying(!isPlaying)
        break
      case 'ArrowUp':
        e.preventDefault()
        navigateToPrevious()
        break
      case 'ArrowDown':
        e.preventDefault()
        navigateToNext()
        break
      case 'm':
      case 'M':
        e.preventDefault()
        handleMuteToggle()
        break
    }
  }

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isPlaying, currentIndex])

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-black">
        <div className="text-white">Loading Shorts...</div>
      </div>
    )
  }

  if (shorts.length === 0) {
    return (
      <div className="h-screen flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">No Shorts Available</h2>
          <p className="text-gray-400">Check back later for short videos!</p>
        </div>
      </div>
    )
  }

  const currentShort = shorts[currentIndex]

  return (
    <div className="h-screen bg-black overflow-hidden relative" ref={containerRef}>
      {/* Video Container */}
      <div className="relative h-full flex items-center justify-center">
        <video
          ref={(el) => (videoRefs.current[currentIndex] = el)}
          src={currentShort.video_url}
          className="h-full w-auto max-w-full object-contain"
          loop
          muted={isMuted}
          playsInline
          onClick={handleVideoClick}
          onEnded={() => navigateToNext()}
          onTimeUpdate={(e) => {
            const video = e.target as HTMLVideoElement
            if (user && video.currentTime > 0) {
              addToHistory(currentShort.id, Math.floor(video.currentTime))
            }
          }}
        />

        {/* Play/Pause Overlay */}
        {!isPlaying && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-black/50 rounded-full p-4">
              <Play className="w-12 h-12 text-white" />
            </div>
          </div>
        )}

        {/* Navigation Arrows */}
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={navigateToPrevious}
            disabled={currentIndex === 0}
            className="bg-black/20 text-white hover:bg-black/40 disabled:opacity-30"
          >
            <ChevronUp className="w-6 h-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={navigateToNext}
            disabled={currentIndex === shorts.length - 1}
            className="bg-black/20 text-white hover:bg-black/40 disabled:opacity-30"
          >
            <ChevronDown className="w-6 h-6" />
          </Button>
        </div>

        {/* Controls */}
        <div className="absolute bottom-4 left-4 right-20 text-white">
          {/* Video Info */}
          <div className="mb-4">
            <h3 className="font-semibold text-lg mb-2 line-clamp-2">
              {currentShort.title}
            </h3>
            {currentShort.description && (
              <p className="text-sm text-gray-300 line-clamp-3 mb-3">
                {currentShort.description}
              </p>
            )}
            
            {/* Channel Info */}
            <div className="flex items-center space-x-3">
              <Link href={`/channel/${currentShort.user_id}`}>
                <Avatar className="w-10 h-10 border-2 border-white">
                  <AvatarImage src={currentShort.profiles.avatar_url || ''} />
                  <AvatarFallback>
                    {currentShort.profiles.username.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </Link>
              <div className="flex-1">
                <Link href={`/channel/${currentShort.user_id}`}>
                  <p className="font-medium hover:underline">
                    @{currentShort.profiles.username}
                  </p>
                </Link>
              </div>
              <SubscribeButton 
                channelId={currentShort.user_id}
                channelName={currentShort.profiles.username}
                className="text-sm"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="absolute bottom-4 right-4 flex flex-col space-y-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleLike(currentShort.id)}
            className="bg-black/20 text-white hover:bg-black/40 flex-col h-auto py-2"
          >
            <ThumbsUp className="w-6 h-6 mb-1" />
            <span className="text-xs">{currentShort.likes}</span>
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDislike(currentShort.id)}
            className="bg-black/20 text-white hover:bg-black/40 flex-col h-auto py-2"
          >
            <ThumbsDown className="w-6 h-6 mb-1" />
            <span className="text-xs">{currentShort.dislikes}</span>
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className="bg-black/20 text-white hover:bg-black/40 flex-col h-auto py-2"
          >
            <MessageCircle className="w-6 h-6 mb-1" />
            <span className="text-xs">Comment</span>
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleShare(currentShort)}
            className="bg-black/20 text-white hover:bg-black/40 flex-col h-auto py-2"
          >
            <Share className="w-6 h-6 mb-1" />
            <span className="text-xs">Share</span>
          </Button>
          
          <AddToPlaylistButton 
            videoId={currentShort.id}
            className="bg-black/20 text-white hover:bg-black/40"
          />
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleMuteToggle}
            className="bg-black/20 text-white hover:bg-black/40"
          >
            {isMuted ? <VolumeX className="w-6 h-6" /> : <Volume2 className="w-6 h-6" />}
          </Button>
        </div>

        {/* Progress Indicator */}
        <div className="absolute top-4 right-4 text-white text-sm bg-black/50 px-2 py-1 rounded">
          {currentIndex + 1} / {shorts.length}
        </div>
      </div>
    </div>
  )
}
