import { create } from 'zustand'
import { supabase, Database } from '@/lib/supabase'

type Subscription = Database['public']['Tables']['subscriptions']['Row']
type Profile = Database['public']['Tables']['profiles']['Row']

interface SubscriptionState {
  subscriptions: (Subscription & { profiles: Profile })[]
  subscribedChannels: string[]
  loading: boolean
  subscribe: (channelId: string) => Promise<void>
  unsubscribe: (channelId: string) => Promise<void>
  fetchSubscriptions: (userId: string) => Promise<void>
  isSubscribed: (channelId: string) => boolean
  getSubscriberCount: (channelId: string) => Promise<number>
}

export const useSubscriptionStore = create<SubscriptionState>((set, get) => ({
  subscriptions: [],
  subscribedChannels: [],
  loading: false,

  subscribe: async (channelId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('subscriptions')
        .insert({
          subscriber_id: user.id,
          channel_id: channelId,
        })

      if (error) throw error

      // Update local state
      set(state => ({
        subscribedChannels: [...state.subscribedChannels, channelId]
      }))

      // Refresh subscriptions
      get().fetchSubscriptions(user.id)
    } catch (error) {
      console.error('Error subscribing:', error)
      throw error
    }
  },

  unsubscribe: async (channelId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('subscriptions')
        .delete()
        .eq('subscriber_id', user.id)
        .eq('channel_id', channelId)

      if (error) throw error

      // Update local state
      set(state => ({
        subscribedChannels: state.subscribedChannels.filter(id => id !== channelId)
      }))

      // Refresh subscriptions
      get().fetchSubscriptions(user.id)
    } catch (error) {
      console.error('Error unsubscribing:', error)
      throw error
    }
  },

  fetchSubscriptions: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          profiles:channel_id (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('subscriber_id', userId)

      if (error) throw error

      const subscriptions = data || []
      const subscribedChannels = subscriptions.map(sub => sub.channel_id)

      set({ 
        subscriptions,
        subscribedChannels
      })
    } catch (error) {
      console.error('Error fetching subscriptions:', error)
    } finally {
      set({ loading: false })
    }
  },

  isSubscribed: (channelId: string) => {
    return get().subscribedChannels.includes(channelId)
  },

  getSubscriberCount: async (channelId: string) => {
    try {
      const { count, error } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('channel_id', channelId)

      if (error) throw error
      return count || 0
    } catch (error) {
      console.error('Error getting subscriber count:', error)
      return 0
    }
  },
}))
