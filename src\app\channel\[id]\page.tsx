'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { VideoCard } from '@/components/video/VideoCard'
import { SubscribeButton } from '@/components/video/SubscribeButton'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { supabase } from '@/lib/supabase'
import { useSubscriptionStore } from '@/store/subscriptionStore'
import { Calendar, MapPin, Link as LinkIcon, Play, Users, Eye } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface ChannelProfile {
  id: string
  username: string
  full_name: string | null
  avatar_url: string | null
  bio: string | null
  created_at: string
}

interface ChannelVideo {
  id: string
  title: string
  description: string | null
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  views: number
  likes: number
  is_public: boolean
  created_at: string
}

type TabType = 'videos' | 'playlists' | 'about'

export default function ChannelPage() {
  const params = useParams()
  const channelId = params.id as string
  
  const [profile, setProfile] = useState<ChannelProfile | null>(null)
  const [videos, setVideos] = useState<ChannelVideo[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<TabType>('videos')
  const [subscriberCount, setSubscriberCount] = useState(0)
  const [totalViews, setTotalViews] = useState(0)
  
  const { getSubscriberCount } = useSubscriptionStore()

  useEffect(() => {
    if (channelId) {
      fetchChannelData()
    }
  }, [channelId])

  const fetchChannelData = async () => {
    setLoading(true)
    try {
      // Fetch profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', channelId)
        .single()

      if (profileError) throw profileError
      setProfile(profileData)

      // Fetch videos
      const { data: videosData, error: videosError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', channelId)
        .eq('is_public', true)
        .order('created_at', { ascending: false })

      if (videosError) throw videosError
      setVideos(videosData || [])

      // Calculate total views
      const totalViewsCount = (videosData || []).reduce((sum, video) => sum + video.views, 0)
      setTotalViews(totalViewsCount)

      // Get subscriber count
      const subCount = await getSubscriberCount(channelId)
      setSubscriberCount(subCount)

    } catch (error) {
      console.error('Error fetching channel data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          {/* Channel Header Skeleton */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-48 rounded-lg mb-6" />
          <div className="flex items-start space-x-6 mb-6">
            <div className="w-32 h-32 bg-muted rounded-full" />
            <div className="flex-1">
              <div className="h-8 bg-muted rounded mb-2" />
              <div className="h-4 bg-muted rounded w-1/3 mb-4" />
              <div className="h-10 bg-muted rounded w-32" />
            </div>
          </div>
          
          {/* Videos Grid Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="aspect-video bg-muted rounded-lg mb-3" />
                <div className="h-4 bg-muted rounded mb-2" />
                <div className="h-3 bg-muted rounded w-2/3" />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Channel not found</h2>
          <p className="text-muted-foreground">
            This channel doesn't exist or has been removed.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Channel Banner */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-48 relative">
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Channel Info */}
      <div className="px-6 -mt-16 relative z-10">
        <div className="flex flex-col md:flex-row items-start md:items-end space-y-4 md:space-y-0 md:space-x-6 mb-6">
          <Avatar className="w-32 h-32 border-4 border-white shadow-lg">
            <AvatarImage src={profile.avatar_url || ''} />
            <AvatarFallback className="text-2xl">
              {profile.username.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">{profile.full_name || profile.username}</h1>
            <p className="text-muted-foreground mb-2">@{profile.username}</p>
            
            <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-4">
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                {formatNumber(subscriberCount)} subscriber{subscriberCount !== 1 ? 's' : ''}
              </div>
              <div className="flex items-center">
                <Play className="w-4 h-4 mr-1" />
                {videos.length} video{videos.length !== 1 ? 's' : ''}
              </div>
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {formatNumber(totalViews)} total views
              </div>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Joined {formatDistanceToNow(new Date(profile.created_at), { addSuffix: true })}
              </div>
            </div>
            
            {profile.bio && (
              <p className="text-sm mb-4 max-w-2xl">{profile.bio}</p>
            )}
          </div>
          
          <SubscribeButton channelId={channelId} channelName={profile.username} />
        </div>

        {/* Navigation Tabs */}
        <div className="border-b mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'videos' as TabType, label: 'Videos', count: videos.length },
              { id: 'playlists' as TabType, label: 'Playlists', count: 0 },
              { id: 'about' as TabType, label: 'About' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {tab.label}
                {tab.count !== undefined && (
                  <Badge variant="secondary" className="ml-2">
                    {tab.count}
                  </Badge>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="pb-8">
          {activeTab === 'videos' && (
            <div>
              {videos.length === 0 ? (
                <div className="text-center py-12">
                  <Play className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No videos yet</h3>
                  <p className="text-muted-foreground">
                    This channel hasn't uploaded any public videos.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {videos.map((video) => (
                    <VideoCard
                      key={video.id}
                      video={{
                        ...video,
                        profiles: {
                          username: profile.username,
                          avatar_url: profile.avatar_url,
                        },
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'playlists' && (
            <div className="text-center py-12">
              <Play className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No playlists yet</h3>
              <p className="text-muted-foreground">
                This channel hasn't created any public playlists.
              </p>
            </div>
          )}

          {activeTab === 'about' && (
            <div className="max-w-4xl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Description</h3>
                  <p className="text-muted-foreground">
                    {profile.bio || 'No description available.'}
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Stats</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Joined:</span>
                      <span>{new Date(profile.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total views:</span>
                      <span>{totalViews.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Subscribers:</span>
                      <span>{subscriberCount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Videos:</span>
                      <span>{videos.length}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
