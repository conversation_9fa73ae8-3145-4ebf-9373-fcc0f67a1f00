import { create } from 'zustand'
import { supabase, Database } from '@/lib/supabase'

type Notification = Database['public']['Tables']['notifications']['Row']

interface NotificationState {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  fetchNotifications: (userId: string) => Promise<void>
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: (userId: string) => Promise<void>
  createNotification: (data: {
    userId: string
    type: 'video_upload' | 'comment' | 'like' | 'subscription' | 'live_stream'
    title: string
    message: string
    actionUrl?: string
    thumbnailUrl?: string
    fromUserId?: string
  }) => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  loading: false,

  fetchNotifications: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select(`
          *,
          from_user:from_user_id (
            username,
            avatar_url
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) throw error

      const notifications = data || []
      const unreadCount = notifications.filter(n => !n.is_read).length

      set({ notifications, unreadCount })
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      set({ loading: false })
    }
  },

  markAsRead: async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)

      if (error) throw error

      // Update local state
      set(state => ({
        notifications: state.notifications.map(n =>
          n.id === notificationId ? { ...n, is_read: true } : n
        ),
        unreadCount: Math.max(0, state.unreadCount - 1)
      }))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  },

  markAllAsRead: async (userId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) throw error

      // Update local state
      set(state => ({
        notifications: state.notifications.map(n => ({ ...n, is_read: true })),
        unreadCount: 0
      }))
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
    }
  },

  createNotification: async (data) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          action_url: data.actionUrl,
          thumbnail_url: data.thumbnailUrl,
          from_user_id: data.fromUserId,
        })

      if (error) throw error
    } catch (error) {
      console.error('Error creating notification:', error)
    }
  },

  deleteNotification: async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)

      if (error) throw error

      // Update local state
      set(state => ({
        notifications: state.notifications.filter(n => n.id !== notificationId),
        unreadCount: state.notifications.find(n => n.id === notificationId && !n.is_read)
          ? state.unreadCount - 1
          : state.unreadCount
      }))
    } catch (error) {
      console.error('Error deleting notification:', error)
    }
  },
}))
