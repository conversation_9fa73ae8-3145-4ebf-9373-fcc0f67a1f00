{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/video/VideoCard.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { formatDistanceToNow } from 'date-fns'\nimport { Eye, Clock } from 'lucide-react'\nimport Link from 'next/link'\nimport Image from 'next/image'\n\ninterface VideoCardProps {\n  video: {\n    id: string\n    title: string\n    description: string | null\n    video_url: string\n    thumbnail_url: string | null\n    duration: number | null\n    views: number\n    likes: number\n    created_at: string\n    profiles?: {\n      username: string\n      avatar_url: string | null\n    }\n  }\n}\n\nexport function VideoCard({ video }: VideoCardProps) {\n  const formatDuration = (seconds: number | null) => {\n    if (!seconds) return '0:00'\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const formatViews = (views: number) => {\n    if (views >= 1000000) {\n      return `${(views / 1000000).toFixed(1)}M`\n    } else if (views >= 1000) {\n      return `${(views / 1000).toFixed(1)}K`\n    }\n    return views.toString()\n  }\n\n  return (\n    <Link href={`/watch/${video.id}`}>\n      <Card className=\"group cursor-pointer hover:shadow-lg transition-shadow\">\n        <CardContent className=\"p-0\">\n          <div className=\"relative aspect-video bg-muted rounded-t-lg overflow-hidden\">\n            {video.thumbnail_url ? (\n              <Image\n                src={video.thumbnail_url}\n                alt={video.title}\n                fill\n                className=\"object-cover group-hover:scale-105 transition-transform duration-200\"\n              />\n            ) : (\n              <div className=\"w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                <span className=\"text-white text-lg font-semibold\">\n                  {video.title.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            \n            {video.duration && (\n              <Badge \n                variant=\"secondary\" \n                className=\"absolute bottom-2 right-2 bg-black/80 text-white hover:bg-black/80\"\n              >\n                <Clock className=\"w-3 h-3 mr-1\" />\n                {formatDuration(video.duration)}\n              </Badge>\n            )}\n          </div>\n          \n          <div className=\"p-4\">\n            <div className=\"flex space-x-3\">\n              <Avatar className=\"w-9 h-9 flex-shrink-0\">\n                <AvatarImage src={video.profiles?.avatar_url || ''} />\n                <AvatarFallback>\n                  {video.profiles?.username?.charAt(0).toUpperCase() || 'U'}\n                </AvatarFallback>\n              </Avatar>\n              \n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors\">\n                  {video.title}\n                </h3>\n                \n                <p className=\"text-muted-foreground text-xs mt-1\">\n                  {video.profiles?.username || 'Unknown User'}\n                </p>\n                \n                <div className=\"flex items-center space-x-2 text-muted-foreground text-xs mt-1\">\n                  <div className=\"flex items-center\">\n                    <Eye className=\"w-3 h-3 mr-1\" />\n                    {formatViews(video.views)} views\n                  </div>\n                  <span>•</span>\n                  <span>\n                    {formatDistanceToNow(new Date(video.created_at), { addSuffix: true })}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;AAFA;AAAA;AADA;AALA;;;;;;;;;AA4BO,SAAS,UAAU,EAAE,KAAK,EAAkB;IACjD,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,SAAS;YACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;kBAC9B,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,aAAa,iBAClB,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,MAAM,aAAa;gCACxB,KAAK,MAAM,KAAK;gCAChB,IAAI;gCACJ,WAAU;;;;;qDAGZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;4BAKvC,MAAM,QAAQ,kBACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,eAAe,MAAM,QAAQ;;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,MAAM,QAAQ,EAAE,cAAc;;;;;;sDAChD,8OAAC,kIAAA,CAAA,iBAAc;sDACZ,MAAM,QAAQ,EAAE,UAAU,OAAO,GAAG,iBAAiB;;;;;;;;;;;;8CAI1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAGd,8OAAC;4CAAE,WAAU;sDACV,MAAM,QAAQ,EAAE,YAAY;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd,YAAY,MAAM,KAAK;wDAAE;;;;;;;8DAE5B,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DACE,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,MAAM,UAAU,GAAG;wDAAE,WAAW;oDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvF"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { VideoCard } from '@/components/video/VideoCard'\nimport { useVideoStore } from '@/store/videoStore'\n\nexport default function Home() {\n  const { videos, loading, fetchVideos } = useVideoStore()\n\n  useEffect(() => {\n    fetchVideos()\n  }, [fetchVideos])\n\n  if (loading) {\n    return (\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {Array.from({ length: 8 }).map((_, i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"aspect-video bg-muted rounded-lg mb-3\" />\n              <div className=\"flex space-x-3\">\n                <div className=\"w-9 h-9 bg-muted rounded-full flex-shrink-0\" />\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-muted rounded mb-2\" />\n                  <div className=\"h-3 bg-muted rounded w-2/3\" />\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold\">Recommended</h1>\n        <p className=\"text-muted-foreground\">Discover amazing videos from creators</p>\n      </div>\n\n      {videos.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <h2 className=\"text-xl font-semibold mb-2\">No videos yet</h2>\n          <p className=\"text-muted-foreground\">\n            Be the first to upload a video to this platform!\n          </p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {videos.map((video) => (\n            <VideoCard key={video.id} video={video} />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wBAAY,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;uBANX;;;;;;;;;;;;;;;IAcpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;YAGtC,OAAO,MAAM,KAAK,kBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;qCAKvC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,wIAAA,CAAA,YAAS;wBAAgB,OAAO;uBAAjB,MAAM,EAAE;;;;;;;;;;;;;;;;AAMpC"}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}