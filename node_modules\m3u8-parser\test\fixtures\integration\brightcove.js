module.exports = {
  allowCache: true,
  iFramePlaylists: [],
  dateRanges: [],
  playlists: [
    {
      attributes: {
        'PROGRAM-ID': 1,
        'BANDWIDTH': 240000,
        'RESOLUTION': {
          width: 396,
          height: 224
        }
      },
      timeline: 0,
      uri: 'http://c.brightcove.com/services/mobile/streaming/index/rendition.m3u8?assetId=1824686811001&videoId=1824650741001'
    },
    {
      attributes: {
        'PROGRAM-ID': 1,
        'BANDWIDTH': 40000
      },
      timeline: 0,
      uri: 'http://c.brightcove.com/services/mobile/streaming/index/rendition.m3u8?assetId=1824683759001&videoId=1824650741001'
    },
    {
      attributes: {
        'PROGRAM-ID': 1,
        'BANDWIDTH': 440000,
        'RESOLUTION': {
          width: 396,
          height: 224
        }
      },
      timeline: 0,
      uri: 'http://c.brightcove.com/services/mobile/streaming/index/rendition.m3u8?assetId=1824686593001&videoId=1824650741001'
    },
    {
      attributes: {
        'PROGRAM-ID': 1,
        'BANDWIDTH': 1928000,
        'RESOLUTION': {
          width: 960,
          height: 540
        }
      },
      timeline: 0,
      uri: 'http://c.brightcove.com/services/mobile/streaming/index/rendition.m3u8?assetId=1824687660001&videoId=1824650741001'
    }
  ],
  discontinuityStarts: [],
  mediaGroups: {
    'VIDEO': {},
    'AUDIO': {},
    'CLOSED-CAPTIONS': {},
    'SUBTITLES': {}
  },
  segments: []
};
