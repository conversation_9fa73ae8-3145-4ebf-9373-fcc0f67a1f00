import { create } from 'zustand'
import { supabase, Database } from '@/lib/supabase'

type Video = Database['public']['Tables']['videos']['Row']

interface VideoState {
  videos: Video[]
  currentVideo: Video | null
  loading: boolean
  searchQuery: string
  fetchVideos: () => Promise<void>
  fetchVideoById: (id: string) => Promise<void>
  uploadVideo: (videoData: {
    title: string
    description?: string
    videoFile: File
    thumbnailFile?: File
    category?: string
    tags?: string[]
    isPublic?: boolean
  }) => Promise<void>
  searchVideos: (query: string) => Promise<void>
  incrementViews: (videoId: string) => Promise<void>
  likeVideo: (videoId: string) => Promise<void>
  setSearchQuery: (query: string) => void
}

export const useVideoStore = create<VideoState>((set, get) => ({
  videos: [],
  currentVideo: null,
  loading: false,
  searchQuery: '',

  fetchVideos: async () => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      set({ videos: data || [] })
    } catch (error) {
      console.error('Error fetching videos:', error)
    } finally {
      set({ loading: false })
    }
  },

  fetchVideoById: async (id: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url,
            full_name
          )
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      set({ currentVideo: data })
    } catch (error) {
      console.error('Error fetching video:', error)
    } finally {
      set({ loading: false })
    }
  },

  uploadVideo: async (videoData) => {
    set({ loading: true })
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Upload video file
      const videoFileName = `${Date.now()}-${videoData.videoFile.name}`
      const { data: videoUpload, error: videoError } = await supabase.storage
        .from('videos')
        .upload(videoFileName, videoData.videoFile)

      if (videoError) throw videoError

      // Upload thumbnail if provided
      let thumbnailUrl = null
      if (videoData.thumbnailFile) {
        const thumbnailFileName = `${Date.now()}-${videoData.thumbnailFile.name}`
        const { data: thumbnailUpload, error: thumbnailError } = await supabase.storage
          .from('thumbnails')
          .upload(thumbnailFileName, videoData.thumbnailFile)

        if (thumbnailError) throw thumbnailError

        const { data: thumbnailUrlData } = supabase.storage
          .from('thumbnails')
          .getPublicUrl(thumbnailUpload.path)

        thumbnailUrl = thumbnailUrlData.publicUrl
      }

      // Get video URL
      const { data: videoUrlData } = supabase.storage
        .from('videos')
        .getPublicUrl(videoUpload.path)

      // Insert video record
      const { data, error } = await supabase
        .from('videos')
        .insert({
          title: videoData.title,
          description: videoData.description,
          video_url: videoUrlData.publicUrl,
          thumbnail_url: thumbnailUrl,
          user_id: user.id,
          category: videoData.category,
          tags: videoData.tags,
          is_public: videoData.isPublic ?? true,
        })
        .select()
        .single()

      if (error) throw error

      // Refresh videos list
      get().fetchVideos()
    } catch (error) {
      console.error('Error uploading video:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },

  searchVideos: async (query: string) => {
    set({ loading: true, searchQuery: query })
    try {
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .ilike('title', `%${query}%`)
        .order('created_at', { ascending: false })

      if (error) throw error
      set({ videos: data || [] })
    } catch (error) {
      console.error('Error searching videos:', error)
    } finally {
      set({ loading: false })
    }
  },

  incrementViews: async (videoId: string) => {
    try {
      const { error } = await supabase.rpc('increment_views', {
        video_id: videoId
      })

      if (error) throw error
    } catch (error) {
      console.error('Error incrementing views:', error)
    }
  },

  likeVideo: async (videoId: string) => {
    try {
      const { error } = await supabase.rpc('increment_likes', {
        video_id: videoId
      })

      if (error) throw error
    } catch (error) {
      console.error('Error liking video:', error)
    }
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query })
  },
}))
