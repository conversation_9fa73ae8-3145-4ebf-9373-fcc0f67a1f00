'use client'

import { useEffect, useRef } from 'react'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'

interface VideoPlayerProps {
  src: string
  poster?: string
  videoId?: string
  onReady?: (player: any) => void
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onTimeUpdate?: (currentTime: number, duration: number) => void
}

export function VideoPlayer({
  src,
  poster,
  videoId,
  onReady,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<any>(null)
  const { user } = useAuthStore()
  const { addToHistory } = useHistoryStore()

  useEffect(() => {
    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      const videoElement = document.createElement('video-js')
      videoElement.classList.add('vjs-big-play-centered')

      if (videoRef.current) {
        videoRef.current.appendChild(videoElement)
      }

      const player = playerRef.current = videojs(videoElement, {
        autoplay: false,
        controls: true,
        responsive: true,
        fluid: true,
        sources: [{
          src,
          type: 'video/mp4'
        }],
        poster: poster || undefined,
        playbackRates: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
        plugins: {
          hotkeys: {
            volumeStep: 0.1,
            seekStep: 5,
            enableModifiersForNumbers: false
          }
        }
      }, () => {
        console.log('Video.js player is ready')
        onReady && onReady(player)
      })

      // Event listeners
      if (onPlay) {
        player.on('play', onPlay)
      }

      if (onPause) {
        player.on('pause', onPause)
      }

      if (onEnded) {
        player.on('ended', onEnded)
      }

      // Time update for progress tracking
      player.on('timeupdate', () => {
        const currentTime = player.currentTime()
        const duration = player.duration()

        if (onTimeUpdate) {
          onTimeUpdate(currentTime, duration)
        }

        // Save to watch history every 10 seconds
        if (user && videoId && currentTime > 0 && Math.floor(currentTime) % 10 === 0) {
          addToHistory(videoId, Math.floor(currentTime))
        }
      })

      // Keyboard shortcuts
      player.on('keydown', (event: KeyboardEvent) => {
        switch (event.key) {
          case ' ':
            event.preventDefault()
            if (player.paused()) {
              player.play()
            } else {
              player.pause()
            }
            break
          case 'ArrowLeft':
            event.preventDefault()
            player.currentTime(Math.max(0, player.currentTime() - 10))
            break
          case 'ArrowRight':
            event.preventDefault()
            player.currentTime(Math.min(player.duration(), player.currentTime() + 10))
            break
          case 'ArrowUp':
            event.preventDefault()
            player.volume(Math.min(1, player.volume() + 0.1))
            break
          case 'ArrowDown':
            event.preventDefault()
            player.volume(Math.max(0, player.volume() - 0.1))
            break
          case 'm':
          case 'M':
            event.preventDefault()
            player.muted(!player.muted())
            break
          case 'f':
          case 'F':
            event.preventDefault()
            if (player.isFullscreen()) {
              player.exitFullscreen()
            } else {
              player.requestFullscreen()
            }
            break
        }
      })

    } else {
      // Update source if it changes
      const player = playerRef.current
      player.src({ src, type: 'video/mp4' })
      if (poster) {
        player.poster(poster)
      }
    }
  }, [src, poster, onReady, onPlay, onPause, onEnded, onTimeUpdate, user, videoId, addToHistory])

  // Dispose the Video.js player when the functional component unmounts
  useEffect(() => {
    const player = playerRef.current

    return () => {
      if (player && !player.isDisposed()) {
        player.dispose()
        playerRef.current = null
      }
    }
  }, [])

  return (
    <div className="w-full">
      <div data-vjs-player>
        <div ref={videoRef} className="video-js-container" />
      </div>
    </div>
  )
}
