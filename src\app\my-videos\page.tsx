'use client'

import { useEffect, useState } from 'react'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuthStore } from '@/store/authStore'
import { supabase } from '@/lib/supabase'
import { PlaySquare, Upload, Eye, ThumbsUp, BarChart3, Edit, Trash2, Plus } from 'lucide-react'
import Link from 'next/link'

interface MyVideo {
  id: string
  title: string
  description: string | null
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  views: number
  likes: number
  dislikes: number
  category: string | null
  is_public: boolean
  created_at: string
  updated_at: string
}

type ViewMode = 'grid' | 'list'
type SortBy = 'newest' | 'oldest' | 'most_viewed' | 'most_liked'

export default function MyVideosPage() {
  const { user } = useAuthStore()
  const [videos, setVideos] = useState<MyVideo[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('newest')

  const fetchMyVideos = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      let query = supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)

      // Apply sorting
      switch (sortBy) {
        case 'newest':
          query = query.order('created_at', { ascending: false })
          break
        case 'oldest':
          query = query.order('created_at', { ascending: true })
          break
        case 'most_viewed':
          query = query.order('views', { ascending: false })
          break
        case 'most_liked':
          query = query.order('likes', { ascending: false })
          break
      }

      const { data, error } = await query

      if (error) throw error
      setVideos(data || [])
    } catch (error) {
      console.error('Error fetching my videos:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMyVideos()
  }, [user, sortBy])

  const handleDeleteVideo = async (videoId: string) => {
    if (!confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('videos')
        .delete()
        .eq('id', videoId)

      if (error) throw error

      // Remove from local state
      setVideos(videos.filter(v => v.id !== videoId))
    } catch (error) {
      console.error('Error deleting video:', error)
      alert('Failed to delete video. Please try again.')
    }
  }

  const toggleVideoVisibility = async (videoId: string, isPublic: boolean) => {
    try {
      const { error } = await supabase
        .from('videos')
        .update({ is_public: !isPublic })
        .eq('id', videoId)

      if (error) throw error

      // Update local state
      setVideos(videos.map(v => 
        v.id === videoId 
          ? { ...v, is_public: !isPublic }
          : v
      ))
    } catch (error) {
      console.error('Error updating video visibility:', error)
      alert('Failed to update video visibility. Please try again.')
    }
  }

  const getTotalStats = () => {
    return {
      totalViews: videos.reduce((sum, v) => sum + v.views, 0),
      totalLikes: videos.reduce((sum, v) => sum + v.likes, 0),
      publicVideos: videos.filter(v => v.is_public).length,
      privateVideos: videos.filter(v => !v.is_public).length,
    }
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <PlaySquare className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to manage your videos</h2>
          <p className="text-muted-foreground">
            Upload and manage your video content.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">My Videos</h1>
          <div className="h-4 w-32 bg-muted rounded animate-pulse mt-2" />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="aspect-video bg-muted rounded-lg mb-3" />
              <div className="h-4 bg-muted rounded mb-2" />
              <div className="h-3 bg-muted rounded w-2/3" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  const stats = getTotalStats()

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <PlaySquare className="w-6 h-6 mr-2 text-green-600" />
            <h1 className="text-2xl font-bold">My Videos</h1>
          </div>
          
          <Link href="/upload">
            <Button className="flex items-center">
              <Plus className="w-4 h-4 mr-1" />
              Upload Video
            </Button>
          </Link>
        </div>

        {/* Stats Overview */}
        {videos.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card p-4 rounded-lg border">
              <div className="flex items-center">
                <PlaySquare className="w-5 h-5 mr-2 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Videos</p>
                  <p className="text-xl font-bold">{videos.length}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-card p-4 rounded-lg border">
              <div className="flex items-center">
                <Eye className="w-5 h-5 mr-2 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Views</p>
                  <p className="text-xl font-bold">{stats.totalViews.toLocaleString()}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-card p-4 rounded-lg border">
              <div className="flex items-center">
                <ThumbsUp className="w-5 h-5 mr-2 text-red-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Likes</p>
                  <p className="text-xl font-bold">{stats.totalLikes.toLocaleString()}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-card p-4 rounded-lg border">
              <div className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-purple-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Public/Private</p>
                  <p className="text-xl font-bold">{stats.publicVideos}/{stats.privateVideos}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Controls */}
        {videos.length > 0 && (
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="most_viewed">Most Viewed</option>
                <option value="most_liked">Most Liked</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                List
              </Button>
            </div>
          </div>
        )}
      </div>

      {videos.length === 0 ? (
        <div className="text-center py-12">
          <Upload className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No videos uploaded yet</h2>
          <p className="text-muted-foreground mb-4">
            Start sharing your content with the world by uploading your first video.
          </p>
          <Link href="/upload">
            <Button>
              <Upload className="w-4 h-4 mr-2" />
              Upload Your First Video
            </Button>
          </Link>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {videos.map((video) => (
            <div key={video.id} className="relative group">
              {/* Video visibility indicator */}
              <div className="absolute top-2 left-2 z-10">
                <Badge variant={video.is_public ? 'default' : 'secondary'}>
                  {video.is_public ? 'Public' : 'Private'}
                </Badge>
              </div>
              
              {/* Action buttons */}
              <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => toggleVideoVisibility(video.id, video.is_public)}
                    title={video.is_public ? 'Make Private' : 'Make Public'}
                  >
                    <Eye className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    title="Edit Video"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeleteVideo(video.id)}
                    title="Delete Video"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <VideoCard video={{
                ...video,
                profiles: {
                  username: user.user_metadata?.username || 'You',
                  avatar_url: user.user_metadata?.avatar_url || null
                }
              }} />
              
              {/* Additional stats */}
              <div className="mt-2 px-2">
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <span>{video.views} views</span>
                    <span>•</span>
                    <span>{video.likes} likes</span>
                  </div>
                  <span>{new Date(video.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {videos.map((video) => (
            <div key={video.id} className="flex items-start space-x-4 p-4 bg-card rounded-lg border">
              <Link href={`/watch/${video.id}`} className="flex-shrink-0">
                <div className="relative w-40 aspect-video bg-muted rounded overflow-hidden">
                  {video.thumbnail_url ? (
                    <img
                      src={video.thumbnail_url}
                      alt={video.title}
                      className="w-full h-full object-cover hover:scale-105 transition-transform"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                      <span className="text-white text-lg font-semibold">
                        {video.title.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>
              </Link>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-2">
                  <Link href={`/watch/${video.id}`}>
                    <h3 className="font-semibold text-sm line-clamp-2 hover:text-primary transition-colors">
                      {video.title}
                    </h3>
                  </Link>
                  
                  <div className="flex items-center space-x-1 ml-4">
                    <Badge variant={video.is_public ? 'default' : 'secondary'} className="text-xs">
                      {video.is_public ? 'Public' : 'Private'}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                  {video.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-xs text-muted-foreground space-x-4">
                    <span>{video.views.toLocaleString()} views</span>
                    <span>{video.likes} likes</span>
                    <span>Uploaded {new Date(video.created_at).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleVideoVisibility(video.id, video.is_public)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeleteVideo(video.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
