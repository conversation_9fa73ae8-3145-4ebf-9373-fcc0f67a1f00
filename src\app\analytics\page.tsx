'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuthStore } from '@/store/authStore'
import { supabase } from '@/lib/supabase'
import { 
  BarChart3, 
  TrendingUp, 
  Eye, 
  ThumbsUp, 
  Users, 
  Clock,
  Calendar,
  Download,
  Filter
} from 'lucide-react'

interface AnalyticsData {
  totalViews: number
  totalLikes: number
  totalSubscribers: number
  totalWatchTime: number
  viewsGrowth: number
  likesGrowth: number
  subscribersGrowth: number
  topVideos: Array<{
    id: string
    title: string
    views: number
    likes: number
    watch_time: number
    thumbnail_url: string | null
  }>
  viewsByDate: Array<{
    date: string
    views: number
    watch_time: number
  }>
  audienceRetention: Array<{
    timestamp: number
    retention_rate: number
  }>
  trafficSources: Array<{
    source: string
    views: number
    percentage: number
  }>
  demographics: {
    countries: Array<{ country: string; percentage: number }>
    ageGroups: Array<{ age_group: string; percentage: number }>
    devices: Array<{ device: string; percentage: number }>
  }
}

export default function AnalyticsPage() {
  const { user } = useAuthStore()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '28d' | '90d' | '1y'>('28d')

  useEffect(() => {
    if (user) {
      fetchAnalytics()
    }
  }, [user, timeRange])

  const fetchAnalytics = async () => {
    if (!user) return

    setLoading(true)
    try {
      // This would typically call a comprehensive analytics API
      // For now, we'll simulate the data structure
      
      const { data: videos, error: videosError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)

      if (videosError) throw videosError

      const { data: subscribers, error: subsError } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact' })
        .eq('channel_id', user.id)

      if (subsError) throw subsError

      // Calculate analytics
      const totalViews = videos?.reduce((sum, video) => sum + video.views, 0) || 0
      const totalLikes = videos?.reduce((sum, video) => sum + video.likes, 0) || 0
      const totalSubscribers = subscribers?.length || 0

      // Simulate more complex analytics data
      const mockAnalytics: AnalyticsData = {
        totalViews,
        totalLikes,
        totalSubscribers,
        totalWatchTime: Math.floor(totalViews * 0.6 * 180), // Estimated watch time
        viewsGrowth: Math.floor(Math.random() * 20) - 10, // -10% to +10%
        likesGrowth: Math.floor(Math.random() * 30) - 15,
        subscribersGrowth: Math.floor(Math.random() * 25) - 12,
        topVideos: videos?.slice(0, 5).map(video => ({
          id: video.id,
          title: video.title,
          views: video.views,
          likes: video.likes,
          watch_time: Math.floor(video.views * 0.6 * 180),
          thumbnail_url: video.thumbnail_url
        })) || [],
        viewsByDate: generateDateSeries(timeRange),
        audienceRetention: generateRetentionData(),
        trafficSources: [
          { source: 'YouTube Search', views: Math.floor(totalViews * 0.4), percentage: 40 },
          { source: 'Suggested Videos', views: Math.floor(totalViews * 0.3), percentage: 30 },
          { source: 'External', views: Math.floor(totalViews * 0.15), percentage: 15 },
          { source: 'Direct', views: Math.floor(totalViews * 0.15), percentage: 15 },
        ],
        demographics: {
          countries: [
            { country: 'United States', percentage: 35 },
            { country: 'United Kingdom', percentage: 15 },
            { country: 'Canada', percentage: 12 },
            { country: 'Australia', percentage: 8 },
            { country: 'Germany', percentage: 7 },
          ],
          ageGroups: [
            { age_group: '18-24', percentage: 25 },
            { age_group: '25-34', percentage: 35 },
            { age_group: '35-44', percentage: 20 },
            { age_group: '45-54', percentage: 12 },
            { age_group: '55+', percentage: 8 },
          ],
          devices: [
            { device: 'Mobile', percentage: 65 },
            { device: 'Desktop', percentage: 25 },
            { device: 'Tablet', percentage: 7 },
            { device: 'TV', percentage: 3 },
          ]
        }
      }

      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateDateSeries = (range: string) => {
    const days = range === '7d' ? 7 : range === '28d' ? 28 : range === '90d' ? 90 : 365
    const data = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      data.push({
        date: date.toISOString().split('T')[0],
        views: Math.floor(Math.random() * 1000) + 100,
        watch_time: Math.floor(Math.random() * 5000) + 500
      })
    }
    
    return data
  }

  const generateRetentionData = () => {
    const data = []
    for (let i = 0; i <= 100; i += 5) {
      data.push({
        timestamp: i,
        retention_rate: Math.max(10, 100 - (i * 0.8) - Math.random() * 20)
      })
    }
    return data
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <BarChart3 className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to view analytics</h2>
          <p className="text-muted-foreground">
            Track your channel's performance and audience insights.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Analytics</h1>
          <div className="h-4 w-32 bg-muted rounded animate-pulse mt-2" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded mb-2" />
                <div className="h-8 bg-muted rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics) return null

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">Channel Analytics</h1>
            <p className="text-muted-foreground">
              Track your channel's performance and audience insights
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {(['7d', '28d', '90d', '1y'] as const).map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange(range)}
                >
                  {range === '7d' ? '7 days' : 
                   range === '28d' ? '28 days' : 
                   range === '90d' ? '90 days' : '1 year'}
                </Button>
              ))}
            </div>
            
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Views</p>
                <p className="text-2xl font-bold">{formatNumber(analytics.totalViews)}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className={`w-3 h-3 mr-1 ${analytics.viewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                  <span className={`text-xs ${analytics.viewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {analytics.viewsGrowth >= 0 ? '+' : ''}{analytics.viewsGrowth}%
                  </span>
                </div>
              </div>
              <Eye className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Likes</p>
                <p className="text-2xl font-bold">{formatNumber(analytics.totalLikes)}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className={`w-3 h-3 mr-1 ${analytics.likesGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                  <span className={`text-xs ${analytics.likesGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {analytics.likesGrowth >= 0 ? '+' : ''}{analytics.likesGrowth}%
                  </span>
                </div>
              </div>
              <ThumbsUp className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Subscribers</p>
                <p className="text-2xl font-bold">{formatNumber(analytics.totalSubscribers)}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className={`w-3 h-3 mr-1 ${analytics.subscribersGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`} />
                  <span className={`text-xs ${analytics.subscribersGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {analytics.subscribersGrowth >= 0 ? '+' : ''}{analytics.subscribersGrowth}%
                  </span>
                </div>
              </div>
              <Users className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Watch Time</p>
                <p className="text-2xl font-bold">{formatDuration(analytics.totalWatchTime)}</p>
                <div className="flex items-center mt-1">
                  <Clock className="w-3 h-3 mr-1 text-purple-600" />
                  <span className="text-xs text-muted-foreground">
                    Avg: {formatDuration(analytics.totalWatchTime / Math.max(analytics.totalViews, 1))}
                  </span>
                </div>
              </div>
              <Clock className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Top Videos */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Videos</CardTitle>
            <CardDescription>Your most viewed videos in the selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topVideos.map((video, index) => (
                <div key={video.id} className="flex items-center space-x-3">
                  <div className="text-sm font-medium text-muted-foreground w-6">
                    #{index + 1}
                  </div>
                  
                  <div className="w-16 h-10 bg-muted rounded overflow-hidden flex-shrink-0">
                    {video.thumbnail_url ? (
                      <img
                        src={video.thumbnail_url}
                        alt={video.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium line-clamp-1">{video.title}</p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>{formatNumber(video.views)} views</span>
                      <span>{formatNumber(video.likes)} likes</span>
                      <span>{formatDuration(video.watch_time)} watch time</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Traffic Sources */}
        <Card>
          <CardHeader>
            <CardTitle>Traffic Sources</CardTitle>
            <CardDescription>Where your views are coming from</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.trafficSources.map((source) => (
                <div key={source.source} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-primary rounded-full" />
                    <span className="text-sm font-medium">{source.source}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{source.percentage}%</div>
                    <div className="text-xs text-muted-foreground">
                      {formatNumber(source.views)} views
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Demographics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Countries</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.demographics.countries.map((country) => (
                <div key={country.country} className="flex items-center justify-between">
                  <span className="text-sm">{country.country}</span>
                  <span className="text-sm font-medium">{country.percentage}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Age Groups</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.demographics.ageGroups.map((group) => (
                <div key={group.age_group} className="flex items-center justify-between">
                  <span className="text-sm">{group.age_group}</span>
                  <span className="text-sm font-medium">{group.percentage}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Devices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.demographics.devices.map((device) => (
                <div key={device.device} className="flex items-center justify-between">
                  <span className="text-sm">{device.device}</span>
                  <span className="text-sm font-medium">{device.percentage}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
