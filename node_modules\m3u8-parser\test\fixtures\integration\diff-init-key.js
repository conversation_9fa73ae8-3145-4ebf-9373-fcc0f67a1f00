module.exports = {
  allowCache: true,
  discontinuitySequence: 0,
  discontinuityStarts: [],
  dateRanges: [],
  iFramePlaylists: [],
  mediaSequence: 7794,
  segments: [
    {
      duration: 2.833,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=52'
        },
        uri: 'http://media.example.com/init52.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence52-A.m4s'
    },
    {
      duration: 15,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=52'
        },
        uri: 'http://media.example.com/init52.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence52-B.m4s'
    },
    {
      duration: 13.333,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=52'
        },
        uri: 'http://media.example.com/init52.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence52-C.m4s'
    },
    {
      duration: 15,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=53'
      },
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=53'
        },
        uri: 'http://media.example.com/init53-A.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence53-A.m4s'
    },
    {
      duration: 14,
      key: {
        iv: new Uint32Array([0, 0, 331, 3063767524]),
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=53'
      },
      map: {
        key: {
          iv: new Uint32Array([0, 0, 331, 3063767524]),
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=53'
        },
        uri: 'http://media.example.com/init53-B.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence53-B.m4s'
    },
    {
      duration: 12,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=54'
      },
      map: {
        uri: 'http://media.example.com/init54-A.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-A.m4s'
    },
    {
      duration: 13,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=54'
      },
      map: {
        uri: 'http://media.example.com/init54-A.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-B.m4s'
    },
    {
      duration: 10,
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=54'
        },
        uri: 'http://media.example.com/init54-B.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-A.m4s'
    },
    {
      duration: 11,
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=54'
        },
        uri: 'http://media.example.com/init54-B.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-B.m4s'
    },
    {
      duration: 4,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=54-b'
      },
      map: {
        key: {
          method: 'AES-128',
          uri: 'https://priv.example.com/key.php?r=54-a'
        },
        uri: 'http://media.example.com/init54-D.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-A.m4s'
    },
    {
      duration: 12,
      map: {
        uri: 'http://media.example.com/init54-E.mp4'
      },
      timeline: 0,
      uri: 'http://media.example.com/fileSequence54-A.m4s'
    }
  ],
  targetDuration: 15,
  version: 7
};
