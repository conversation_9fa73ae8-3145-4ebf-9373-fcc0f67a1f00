'use client'

import { useEffect } from 'react'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'
import { Clock, Trash2, Play } from 'lucide-react'
import Link from 'next/link'

export default function WatchLaterPage() {
  const { user } = useAuthStore()
  const { watchLater, loading, fetchWatchLater, removeFromWatchLater } = useHistoryStore()

  useEffect(() => {
    if (user) {
      fetchWatchLater(user.id)
    }
  }, [user, fetchWatchLater])

  const handleRemoveFromWatchLater = async (videoId: string) => {
    try {
      await removeFromWatchLater(videoId)
    } catch (error) {
      console.error('Error removing from watch later:', error)
    }
  }

  const handlePlayAll = () => {
    if (watchLater.length > 0) {
      // Navigate to first video with playlist context
      window.location.href = `/watch/${watchLater[0].video_id}?playlist=watch-later`
    }
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Clock className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to access Watch Later</h2>
          <p className="text-muted-foreground">
            Save videos to watch them later across all your devices.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Watch Later</h1>
          <div className="h-4 w-32 bg-muted rounded animate-pulse mt-2" />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="aspect-video bg-muted rounded-lg mb-3" />
              <div className="flex space-x-3">
                <div className="w-9 h-9 bg-muted rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded mb-2" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Clock className="w-6 h-6 mr-2 text-blue-600" />
            <h1 className="text-2xl font-bold">Watch Later</h1>
          </div>
          
          {watchLater.length > 0 && (
            <div className="flex items-center space-x-2">
              <Button onClick={handlePlayAll} className="flex items-center">
                <Play className="w-4 h-4 mr-1" />
                Play All
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  if (confirm('Are you sure you want to clear your Watch Later list?')) {
                    watchLater.forEach(item => handleRemoveFromWatchLater(item.video_id))
                  }
                }}
                className="flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Clear All
              </Button>
            </div>
          )}
        </div>
        
        <p className="text-muted-foreground">
          {watchLater.length} video{watchLater.length !== 1 ? 's' : ''} saved
        </p>
      </div>

      {watchLater.length === 0 ? (
        <div className="text-center py-12">
          <Clock className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No videos saved</h2>
          <p className="text-muted-foreground mb-4">
            Videos you save to watch later will appear here.
          </p>
          <Link href="/">
            <Button>Browse Videos</Button>
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {/* List View for Watch Later */}
          <div className="grid gap-4">
            {watchLater.map((item, index) => (
              <div key={item.id} className="flex items-start space-x-4 p-4 bg-card rounded-lg border">
                <div className="text-sm text-muted-foreground font-mono w-8">
                  {index + 1}
                </div>
                
                <div className="flex-1 flex space-x-4">
                  <Link href={`/watch/${item.video_id}`} className="flex-shrink-0">
                    <div className="relative w-40 aspect-video bg-muted rounded overflow-hidden">
                      {item.videos.thumbnail_url ? (
                        <img
                          src={item.videos.thumbnail_url}
                          alt={item.videos.title}
                          className="w-full h-full object-cover hover:scale-105 transition-transform"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-white text-lg font-semibold">
                            {item.videos.title.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                  </Link>
                  
                  <div className="flex-1 min-w-0">
                    <Link href={`/watch/${item.video_id}`}>
                      <h3 className="font-semibold text-sm line-clamp-2 hover:text-primary transition-colors mb-1">
                        {item.videos.title}
                      </h3>
                    </Link>
                    
                    <p className="text-xs text-muted-foreground mb-2">
                      {(item.videos as any).profiles?.username || 'Unknown User'}
                    </p>
                    
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {item.videos.description}
                    </p>
                    
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center text-xs text-muted-foreground">
                        <span>{item.videos.views.toLocaleString()} views</span>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFromWatchLater(item.video_id)}
                        className="text-muted-foreground hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
