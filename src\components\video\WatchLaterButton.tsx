'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'
import { Clock, Check } from 'lucide-react'

interface WatchLaterButtonProps {
  videoId: string
  className?: string
}

export function WatchLaterButton({ videoId, className }: WatchLaterButtonProps) {
  const { user } = useAuthStore()
  const { addToWatchLater, removeFromWatchLater, isInWatchLater, fetchWatchLater } = useHistoryStore()
  const [loading, setLoading] = useState(false)

  const inWatchLater = isInWatchLater(videoId)

  useEffect(() => {
    if (user) {
      fetchWatchLater(user.id)
    }
  }, [user, fetchWatchLater])

  const handleToggleWatchLater = async () => {
    if (!user) {
      alert('Please sign in to save videos for later')
      return
    }

    setLoading(true)
    try {
      if (inWatchLater) {
        await removeFromWatchLater(videoId)
      } else {
        await addToWatchLater(videoId)
      }
    } catch (error) {
      console.error('Error toggling watch later:', error)
      alert('Failed to update watch later list')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleToggleWatchLater}
      disabled={loading || !user}
      className={`flex items-center ${className} ${
        inWatchLater ? 'bg-blue-50 border-blue-200 text-blue-700' : ''
      }`}
    >
      {inWatchLater ? (
        <>
          <Check className="w-4 h-4 mr-1" />
          Saved
        </>
      ) : (
        <>
          <Clock className="w-4 h-4 mr-1" />
          Watch Later
        </>
      )}
    </Button>
  )
}
