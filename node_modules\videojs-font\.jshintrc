{"evil": true, "validthis": true, "node": true, "debug": true, "boss": true, "expr": true, "eqnull": true, "quotmark": "single", "sub": true, "trailing": true, "undef": true, "laxbreak": true, "esnext": true, "eqeqeq": true, "predef": ["_V_", "goog", "console", "require", "define", "module", "exports", "process", "q", "asyncTest", "deepEqual", "equal", "expect", "module", "notDeepEqual", "notEqual", "notStrictEqual", "ok", "QUnit", "raises", "start", "stop", "strictEqual", "test", "sinon"]}