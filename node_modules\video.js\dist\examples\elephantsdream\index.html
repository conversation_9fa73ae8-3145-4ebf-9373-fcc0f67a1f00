<!DOCTYPE html>
<html lang="en">
<head>

  <meta charset="utf-8" />
  <title>Video.js Text Descriptions, Chapters &amp; Captions Example</title>

  <link href="https://vjs.zencdn.net/7.0/video-js.min.css" rel="stylesheet">
  <script src="https://vjs.zencdn.net/7.0/video.min.js"></script>

</head>
<body>

  <!-- NOTE: we have to disable native Text Track support for the HTML5 tech,
             since even HTML5 video players with native Text Track support
             don't currently support 'description' text tracks in any
             useful way! Currently this means that iOS will not display
            ANY text tracks -->
  <video id="example_video_1" class="video-js" controls preload="none" width="640" height="360"
      data-setup='{ "html5" : { "nativeTextTracks" : false } }'
      poster="http://d2zihajmogu5jn.cloudfront.net/elephantsdream/poster.png">

    <source src="//d2zihajmogu5jn.cloudfront.net/elephantsdream/ed_hd.mp4" type="video/mp4">
    <source src="//d2zihajmogu5jn.cloudfront.net/elephantsdream/ed_hd.ogg" type="video/ogg">

    <track kind="captions" src="captions.en.vtt" srclang="en" label="English" default>
    <track kind="captions" src="captions.sv.vtt" srclang="sv" label="Swedish">
    <track kind="captions" src="captions.ru.vtt" srclang="ru" label="Russian">
    <track kind="captions" src="captions.ja.vtt" srclang="ja" label="Japanese">
    <track kind="captions" src="captions.ar.vtt" srclang="ar" label="Arabic">

    <track kind="descriptions" src="descriptions.en.vtt" srclang="en" label="English">

    <track kind="chapters" src="chapters.en.vtt" srclang="en" label="English">

    <p class="vjs-no-js">To view this video please enable JavaScript, and consider upgrading to a web browser that <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a></p>
  </video>

</body>

</html>
