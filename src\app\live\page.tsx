'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/store/authStore'
import { 
  Radio, 
  Users, 
  Eye, 
  MessageCircle, 
  Settings, 
  Share,
  Copy,
  Play,
  Square,
  Mic,
  MicOff,
  Video,
  VideoOff
} from 'lucide-react'
import Link from 'next/link'

interface LiveStream {
  id: string
  title: string
  description: string | null
  stream_key: string
  is_live: boolean
  viewer_count: number
  user_id: string
  created_at: string
  profiles: {
    username: string
    avatar_url: string | null
  }
}

export default function LivePage() {
  const { user } = useAuthStore()
  const [liveStreams, setLiveStreams] = useState<LiveStream[]>([])
  const [myStream, setMyStream] = useState<LiveStream | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [streamTitle, setStreamTitle] = useState('')
  const [streamDescription, setStreamDescription] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchLiveStreams()
    if (user) {
      fetchMyStream()
    }
  }, [user])

  const fetchLiveStreams = async () => {
    try {
      const { data, error } = await supabase
        .from('live_streams')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_live', true)
        .order('viewer_count', { ascending: false })

      if (error) throw error
      setLiveStreams(data || [])
    } catch (error) {
      console.error('Error fetching live streams:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchMyStream = async () => {
    if (!user) return
    
    try {
      const { data, error } = await supabase
        .from('live_streams')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      setMyStream(data)
    } catch (error) {
      console.error('Error fetching my stream:', error)
    }
  }

  const createLiveStream = async () => {
    if (!user || !streamTitle.trim()) return

    setIsCreating(true)
    try {
      const streamKey = `live_${user.id}_${Date.now()}`
      
      const { data, error } = await supabase
        .from('live_streams')
        .insert({
          title: streamTitle.trim(),
          description: streamDescription.trim() || null,
          stream_key: streamKey,
          user_id: user.id,
          is_live: false,
        })
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .single()

      if (error) throw error
      
      setMyStream(data)
      setShowCreateForm(false)
      setStreamTitle('')
      setStreamDescription('')
    } catch (error) {
      console.error('Error creating live stream:', error)
      alert('Failed to create live stream')
    } finally {
      setIsCreating(false)
    }
  }

  const startStream = async () => {
    if (!myStream) return

    try {
      const { error } = await supabase
        .from('live_streams')
        .update({ is_live: true })
        .eq('id', myStream.id)

      if (error) throw error
      
      setMyStream({ ...myStream, is_live: true })
      fetchLiveStreams() // Refresh live streams list
    } catch (error) {
      console.error('Error starting stream:', error)
    }
  }

  const stopStream = async () => {
    if (!myStream) return

    try {
      const { error } = await supabase
        .from('live_streams')
        .update({ is_live: false })
        .eq('id', myStream.id)

      if (error) throw error
      
      setMyStream({ ...myStream, is_live: false })
      fetchLiveStreams() // Refresh live streams list
    } catch (error) {
      console.error('Error stopping stream:', error)
    }
  }

  const copyStreamKey = () => {
    if (myStream) {
      navigator.clipboard.writeText(myStream.stream_key)
      alert('Stream key copied to clipboard!')
    }
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Radio className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to go live</h2>
          <p className="text-muted-foreground">
            Create live streams and connect with your audience in real-time.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Live Streaming</h1>
        <p className="text-muted-foreground">
          Broadcast live to your audience and discover live content from creators.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* My Stream Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Radio className="w-5 h-5 mr-2 text-red-600" />
                Your Stream
              </CardTitle>
              <CardDescription>
                Manage your live stream settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!myStream ? (
                !showCreateForm ? (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground mb-4">
                      You haven't set up a live stream yet.
                    </p>
                    <Button onClick={() => setShowCreateForm(true)}>
                      <Radio className="w-4 h-4 mr-2" />
                      Create Stream
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Stream Title *
                      </label>
                      <Input
                        value={streamTitle}
                        onChange={(e) => setStreamTitle(e.target.value)}
                        placeholder="Enter stream title"
                        maxLength={100}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Description
                      </label>
                      <Textarea
                        value={streamDescription}
                        onChange={(e) => setStreamDescription(e.target.value)}
                        placeholder="Describe your stream"
                        rows={3}
                        maxLength={500}
                      />
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => setShowCreateForm(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={createLiveStream}
                        disabled={!streamTitle.trim() || isCreating}
                        className="flex-1"
                      >
                        {isCreating ? 'Creating...' : 'Create'}
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-1">{myStream.title}</h3>
                    {myStream.description && (
                      <p className="text-sm text-muted-foreground">
                        {myStream.description}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant={myStream.is_live ? 'destructive' : 'secondary'}>
                      {myStream.is_live ? 'LIVE' : 'OFFLINE'}
                    </Badge>
                    {myStream.is_live && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Eye className="w-4 h-4 mr-1" />
                        {myStream.viewer_count} viewers
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Stream Key:</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyStreamKey}
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Copy
                      </Button>
                    </div>
                    <div className="bg-muted p-2 rounded text-xs font-mono break-all">
                      {myStream.stream_key}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    {!myStream.is_live ? (
                      <Button onClick={startStream} className="flex-1">
                        <Play className="w-4 h-4 mr-2" />
                        Go Live
                      </Button>
                    ) : (
                      <Button 
                        variant="destructive" 
                        onClick={stopStream}
                        className="flex-1"
                      >
                        <Square className="w-4 h-4 mr-2" />
                        End Stream
                      </Button>
                    )}
                    <Button variant="outline" size="icon">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className="text-xs text-muted-foreground">
                    <p>Use OBS Studio or similar software to stream.</p>
                    <p>Server: rtmp://live.yoursite.com/live</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Live Streams List */}
        <div className="lg:col-span-2">
          <div className="mb-4">
            <h2 className="text-xl font-semibold mb-2">Live Now</h2>
            <p className="text-muted-foreground">
              {liveStreams.length} creator{liveStreams.length !== 1 ? 's' : ''} streaming live
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="aspect-video bg-muted" />
                  <CardContent className="p-4">
                    <div className="h-4 bg-muted rounded mb-2" />
                    <div className="h-3 bg-muted rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : liveStreams.length === 0 ? (
            <div className="text-center py-12">
              <Radio className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No live streams</h3>
              <p className="text-muted-foreground">
                No one is streaming live right now. Check back later!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {liveStreams.map((stream) => (
                <Card key={stream.id} className="group cursor-pointer hover:shadow-lg transition-shadow">
                  <Link href={`/live/${stream.id}`}>
                    <div className="relative aspect-video bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center">
                      <div className="text-white text-center">
                        <Radio className="w-12 h-12 mx-auto mb-2" />
                        <Badge variant="destructive" className="animate-pulse">
                          LIVE
                        </Badge>
                      </div>
                      
                      <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm flex items-center">
                        <Eye className="w-3 h-3 mr-1" />
                        {stream.viewer_count}
                      </div>
                    </div>
                  </Link>
                  
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <Link href={`/channel/${stream.user_id}`}>
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={stream.profiles.avatar_url || ''} />
                          <AvatarFallback>
                            {stream.profiles.username.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      </Link>
                      
                      <div className="flex-1 min-w-0">
                        <Link href={`/live/${stream.id}`}>
                          <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors">
                            {stream.title}
                          </h3>
                        </Link>
                        
                        <Link href={`/channel/${stream.user_id}`}>
                          <p className="text-xs text-muted-foreground hover:underline">
                            {stream.profiles.username}
                          </p>
                        </Link>
                        
                        {stream.description && (
                          <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                            {stream.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
