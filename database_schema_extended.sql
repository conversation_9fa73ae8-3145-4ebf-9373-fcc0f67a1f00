-- Extended YouTube Clone Database Schema
-- Add these tables to your existing Supabase database

-- Notifications table
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('video_upload', 'comment', 'like', 'subscription', 'live_stream')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  action_url TEXT,
  thumbnail_url TEXT,
  from_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Live streams table
CREATE TABLE live_streams (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  stream_key TEXT UNIQUE NOT NULL,
  is_live BOOLEAN DEFAULT FALSE,
  viewer_count INTEGER DEFAULT 0,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Community posts table
CREATE TABLE community_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('text', 'image', 'video', 'poll')),
  media_url TEXT,
  poll_options TEXT[],
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Community comments table
CREATE TABLE community_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video chapters table
CREATE TABLE video_chapters (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  start_time INTEGER NOT NULL, -- in seconds
  end_time INTEGER, -- in seconds, nullable for last chapter
  thumbnail_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Community post likes table
CREATE TABLE community_post_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
  is_like BOOLEAN NOT NULL, -- true for like, false for dislike
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, post_id)
);

-- Live stream viewers table (for tracking concurrent viewers)
CREATE TABLE live_stream_viewers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES live_streams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  left_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(stream_id, user_id)
);

-- Video analytics table (for detailed analytics)
CREATE TABLE video_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  event_type TEXT NOT NULL CHECK (event_type IN ('view', 'like', 'dislike', 'share', 'comment')),
  watch_duration INTEGER, -- in seconds, for view events
  device_type TEXT,
  country TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

CREATE INDEX idx_live_streams_is_live ON live_streams(is_live);
CREATE INDEX idx_live_streams_user_id ON live_streams(user_id);
CREATE INDEX idx_live_streams_created_at ON live_streams(created_at DESC);

CREATE INDEX idx_community_posts_user_id ON community_posts(user_id);
CREATE INDEX idx_community_posts_created_at ON community_posts(created_at DESC);
CREATE INDEX idx_community_posts_type ON community_posts(type);

CREATE INDEX idx_community_comments_post_id ON community_comments(post_id);
CREATE INDEX idx_community_comments_user_id ON community_comments(user_id);
CREATE INDEX idx_community_comments_created_at ON community_comments(created_at);

CREATE INDEX idx_video_chapters_video_id ON video_chapters(video_id);
CREATE INDEX idx_video_chapters_start_time ON video_chapters(start_time);

CREATE INDEX idx_community_post_likes_post_id ON community_post_likes(post_id);
CREATE INDEX idx_community_post_likes_user_id ON community_post_likes(user_id);

CREATE INDEX idx_live_stream_viewers_stream_id ON live_stream_viewers(stream_id);
CREATE INDEX idx_live_stream_viewers_user_id ON live_stream_viewers(user_id);

CREATE INDEX idx_video_analytics_video_id ON video_analytics(video_id);
CREATE INDEX idx_video_analytics_user_id ON video_analytics(user_id);
CREATE INDEX idx_video_analytics_event_type ON video_analytics(event_type);
CREATE INDEX idx_video_analytics_created_at ON video_analytics(created_at DESC);

-- Row Level Security (RLS) policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_stream_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_analytics ENABLE ROW LEVEL SECURITY;

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON notifications
  FOR INSERT WITH CHECK (true);

-- Live streams policies
CREATE POLICY "Anyone can view live streams" ON live_streams
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own streams" ON live_streams
  FOR ALL USING (auth.uid() = user_id);

-- Community posts policies
CREATE POLICY "Anyone can view community posts" ON community_posts
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own posts" ON community_posts
  FOR ALL USING (auth.uid() = user_id);

-- Community comments policies
CREATE POLICY "Anyone can view comments" ON community_comments
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own comments" ON community_comments
  FOR ALL USING (auth.uid() = user_id);

-- Video chapters policies
CREATE POLICY "Anyone can view video chapters" ON video_chapters
  FOR SELECT USING (true);

CREATE POLICY "Video owners can manage chapters" ON video_chapters
  FOR ALL USING (
    auth.uid() IN (
      SELECT user_id FROM videos WHERE id = video_chapters.video_id
    )
  );

-- Community post likes policies
CREATE POLICY "Users can view all post likes" ON community_post_likes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own post likes" ON community_post_likes
  FOR ALL USING (auth.uid() = user_id);

-- Live stream viewers policies
CREATE POLICY "Users can view stream viewers" ON live_stream_viewers
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own viewer records" ON live_stream_viewers
  FOR ALL USING (auth.uid() = user_id);

-- Video analytics policies
CREATE POLICY "Video owners can view analytics" ON video_analytics
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM videos WHERE id = video_analytics.video_id
    )
  );

CREATE POLICY "System can insert analytics" ON video_analytics
  FOR INSERT WITH CHECK (true);

-- Functions for community post interactions
CREATE OR REPLACE FUNCTION toggle_community_post_like(
  post_id UUID,
  user_id UUID,
  is_like BOOLEAN
)
RETURNS VOID AS $$
BEGIN
  -- Remove existing like/dislike
  DELETE FROM community_post_likes 
  WHERE community_post_likes.post_id = toggle_community_post_like.post_id 
    AND community_post_likes.user_id = toggle_community_post_like.user_id;
  
  -- Insert new like/dislike
  INSERT INTO community_post_likes (post_id, user_id, is_like)
  VALUES (toggle_community_post_like.post_id, toggle_community_post_like.user_id, toggle_community_post_like.is_like);
  
  -- Update post counts
  UPDATE community_posts SET
    likes = (
      SELECT COUNT(*) FROM community_post_likes 
      WHERE community_post_likes.post_id = toggle_community_post_like.post_id 
        AND is_like = true
    ),
    dislikes = (
      SELECT COUNT(*) FROM community_post_likes 
      WHERE community_post_likes.post_id = toggle_community_post_like.post_id 
        AND is_like = false
    )
  WHERE id = toggle_community_post_like.post_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update live stream viewer count
CREATE OR REPLACE FUNCTION update_live_stream_viewer_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE live_streams SET
    viewer_count = (
      SELECT COUNT(*) FROM live_stream_viewers 
      WHERE stream_id = COALESCE(NEW.stream_id, OLD.stream_id)
        AND left_at IS NULL
    )
  WHERE id = COALESCE(NEW.stream_id, OLD.stream_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for live stream viewer count
CREATE TRIGGER update_live_stream_viewer_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON live_stream_viewers
  FOR EACH ROW EXECUTE FUNCTION update_live_stream_viewer_count();

-- Function to update community post comment count
CREATE OR REPLACE FUNCTION update_community_post_comment_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE community_posts SET
    comments_count = (
      SELECT COUNT(*) FROM community_comments 
      WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
    )
  WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for community post comment count
CREATE TRIGGER update_community_post_comment_count_trigger
  AFTER INSERT OR DELETE ON community_comments
  FOR EACH ROW EXECUTE FUNCTION update_community_post_comment_count();

-- Add new columns to existing videos table
ALTER TABLE videos ADD COLUMN IF NOT EXISTS category TEXT;
ALTER TABLE videos ADD COLUMN IF NOT EXISTS tags TEXT[];
ALTER TABLE videos ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT TRUE;

-- Update existing videos to have default values
UPDATE videos SET is_public = TRUE WHERE is_public IS NULL;
UPDATE videos SET category = 'general' WHERE category IS NULL;
UPDATE videos SET tags = '{}' WHERE tags IS NULL;
