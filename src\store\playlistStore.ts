import { create } from 'zustand'
import { supabase, Database } from '@/lib/supabase'

type Playlist = Database['public']['Tables']['playlists']['Row']
type PlaylistVideo = Database['public']['Tables']['playlist_videos']['Row']
type Video = Database['public']['Tables']['videos']['Row']

interface PlaylistState {
  playlists: Playlist[]
  currentPlaylist: (Playlist & { videos: Video[] }) | null
  loading: boolean
  createPlaylist: (data: {
    title: string
    description?: string
    isPublic?: boolean
  }) => Promise<void>
  fetchPlaylists: (userId: string) => Promise<void>
  fetchPlaylistById: (playlistId: string) => Promise<void>
  addVideoToPlaylist: (playlistId: string, videoId: string) => Promise<void>
  removeVideoFromPlaylist: (playlistId: string, videoId: string) => Promise<void>
  deletePlaylist: (playlistId: string) => Promise<void>
  updatePlaylist: (playlistId: string, data: {
    title?: string
    description?: string
    isPublic?: boolean
  }) => Promise<void>
}

export const usePlaylistStore = create<PlaylistState>((set, get) => ({
  playlists: [],
  currentPlaylist: null,
  loading: false,

  createPlaylist: async (data) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('playlists')
        .insert({
          title: data.title,
          description: data.description,
          user_id: user.id,
          is_public: data.isPublic ?? true,
        })

      if (error) throw error

      // Refresh playlists
      get().fetchPlaylists(user.id)
    } catch (error) {
      console.error('Error creating playlist:', error)
      throw error
    }
  },

  fetchPlaylists: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('playlists')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      set({ playlists: data || [] })
    } catch (error) {
      console.error('Error fetching playlists:', error)
    } finally {
      set({ loading: false })
    }
  },

  fetchPlaylistById: async (playlistId: string) => {
    set({ loading: true })
    try {
      // Fetch playlist details
      const { data: playlist, error: playlistError } = await supabase
        .from('playlists')
        .select('*')
        .eq('id', playlistId)
        .single()

      if (playlistError) throw playlistError

      // Fetch playlist videos
      const { data: playlistVideos, error: videosError } = await supabase
        .from('playlist_videos')
        .select(`
          *,
          videos (
            *,
            profiles:user_id (
              username,
              avatar_url
            )
          )
        `)
        .eq('playlist_id', playlistId)
        .order('position')

      if (videosError) throw videosError

      const videos = playlistVideos?.map(pv => pv.videos).filter(Boolean) || []

      set({
        currentPlaylist: {
          ...playlist,
          videos
        }
      })
    } catch (error) {
      console.error('Error fetching playlist:', error)
    } finally {
      set({ loading: false })
    }
  },

  addVideoToPlaylist: async (playlistId: string, videoId: string) => {
    try {
      // Get the next position
      const { count } = await supabase
        .from('playlist_videos')
        .select('*', { count: 'exact', head: true })
        .eq('playlist_id', playlistId)

      const position = (count || 0) + 1

      const { error } = await supabase
        .from('playlist_videos')
        .insert({
          playlist_id: playlistId,
          video_id: videoId,
          position,
        })

      if (error) throw error

      // Refresh current playlist if it's the one being modified
      const currentPlaylist = get().currentPlaylist
      if (currentPlaylist?.id === playlistId) {
        get().fetchPlaylistById(playlistId)
      }
    } catch (error) {
      console.error('Error adding video to playlist:', error)
      throw error
    }
  },

  removeVideoFromPlaylist: async (playlistId: string, videoId: string) => {
    try {
      const { error } = await supabase
        .from('playlist_videos')
        .delete()
        .eq('playlist_id', playlistId)
        .eq('video_id', videoId)

      if (error) throw error

      // Refresh current playlist if it's the one being modified
      const currentPlaylist = get().currentPlaylist
      if (currentPlaylist?.id === playlistId) {
        get().fetchPlaylistById(playlistId)
      }
    } catch (error) {
      console.error('Error removing video from playlist:', error)
      throw error
    }
  },

  deletePlaylist: async (playlistId: string) => {
    try {
      const { error } = await supabase
        .from('playlists')
        .delete()
        .eq('id', playlistId)

      if (error) throw error

      // Update local state
      set(state => ({
        playlists: state.playlists.filter(p => p.id !== playlistId),
        currentPlaylist: state.currentPlaylist?.id === playlistId ? null : state.currentPlaylist
      }))
    } catch (error) {
      console.error('Error deleting playlist:', error)
      throw error
    }
  },

  updatePlaylist: async (playlistId: string, data) => {
    try {
      const { error } = await supabase
        .from('playlists')
        .update({
          title: data.title,
          description: data.description,
          is_public: data.isPublic,
        })
        .eq('id', playlistId)

      if (error) throw error

      // Update local state
      set(state => ({
        playlists: state.playlists.map(p => 
          p.id === playlistId 
            ? { ...p, ...data }
            : p
        ),
        currentPlaylist: state.currentPlaylist?.id === playlistId
          ? { ...state.currentPlaylist, ...data }
          : state.currentPlaylist
      }))
    } catch (error) {
      console.error('Error updating playlist:', error)
      throw error
    }
  },
}))
