'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { usePlaylistStore } from '@/store/playlistStore'
import { useAuthStore } from '@/store/authStore'
import { Plus, ListPlus, Check, X } from 'lucide-react'

interface AddToPlaylistButtonProps {
  videoId: string
  className?: string
}

export function AddToPlaylistButton({ videoId, className }: AddToPlaylistButtonProps) {
  const { user } = useAuthStore()
  const { playlists, fetchPlaylists, createPlaylist, addVideoToPlaylist } = usePlaylistStore()
  const [showModal, setShowModal] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newPlaylistTitle, setNewPlaylistTitle] = useState('')
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user && showModal) {
      fetchPlaylists(user.id)
    }
  }, [user, showModal, fetchPlaylists])

  const handleAddToPlaylist = async (playlistId: string) => {
    setLoading(true)
    try {
      await addVideoToPlaylist(playlistId, videoId)
      setShowModal(false)
    } catch (error) {
      console.error('Error adding to playlist:', error)
      alert('Failed to add video to playlist')
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePlaylist = async () => {
    if (!newPlaylistTitle.trim()) return

    setLoading(true)
    try {
      await createPlaylist({
        title: newPlaylistTitle.trim(),
        description: newPlaylistDescription.trim() || undefined,
        isPublic: true,
      })
      
      setNewPlaylistTitle('')
      setNewPlaylistDescription('')
      setShowCreateForm(false)
      
      // Refresh playlists
      if (user) {
        await fetchPlaylists(user.id)
      }
    } catch (error) {
      console.error('Error creating playlist:', error)
      alert('Failed to create playlist')
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => alert('Please sign in to save videos to playlists')}
        className={className}
      >
        <ListPlus className="w-4 h-4 mr-1" />
        Save
      </Button>
    )
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowModal(true)}
        className={className}
      >
        <ListPlus className="w-4 h-4 mr-1" />
        Save
      </Button>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4 max-h-[80vh] overflow-y-auto">
            <CardHeader className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0"
                onClick={() => {
                  setShowModal(false)
                  setShowCreateForm(false)
                  setNewPlaylistTitle('')
                  setNewPlaylistDescription('')
                }}
              >
                <X className="h-4 w-4" />
              </Button>
              <CardTitle>Save to playlist</CardTitle>
              <CardDescription>
                Choose a playlist or create a new one
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!showCreateForm ? (
                <div className="space-y-3">
                  {/* Create new playlist button */}
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setShowCreateForm(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create new playlist
                  </Button>

                  {/* Existing playlists */}
                  {playlists.length === 0 ? (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No playlists yet. Create your first playlist!
                    </p>
                  ) : (
                    <div className="space-y-2">
                      {playlists.map((playlist) => (
                        <Button
                          key={playlist.id}
                          variant="ghost"
                          className="w-full justify-start h-auto p-3"
                          onClick={() => handleAddToPlaylist(playlist.id)}
                          disabled={loading}
                        >
                          <div className="flex items-center w-full">
                            <div className="w-12 h-8 bg-muted rounded mr-3 flex-shrink-0" />
                            <div className="flex-1 text-left">
                              <p className="font-medium text-sm">{playlist.title}</p>
                              {playlist.description && (
                                <p className="text-xs text-muted-foreground line-clamp-1">
                                  {playlist.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="playlist-title" className="block text-sm font-medium mb-1">
                      Title *
                    </label>
                    <Input
                      id="playlist-title"
                      value={newPlaylistTitle}
                      onChange={(e) => setNewPlaylistTitle(e.target.value)}
                      placeholder="Enter playlist title"
                      maxLength={100}
                    />
                  </div>

                  <div>
                    <label htmlFor="playlist-description" className="block text-sm font-medium mb-1">
                      Description
                    </label>
                    <Textarea
                      id="playlist-description"
                      value={newPlaylistDescription}
                      onChange={(e) => setNewPlaylistDescription(e.target.value)}
                      placeholder="Enter playlist description (optional)"
                      rows={3}
                      maxLength={500}
                    />
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowCreateForm(false)
                        setNewPlaylistTitle('')
                        setNewPlaylistDescription('')
                      }}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreatePlaylist}
                      disabled={!newPlaylistTitle.trim() || loading}
                      className="flex-1"
                    >
                      {loading ? 'Creating...' : 'Create'}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}
