import { create } from 'zustand'
import { supabase, Database } from '@/lib/supabase'

type WatchHistory = Database['public']['Tables']['watch_history']['Row']
type WatchLater = Database['public']['Tables']['watch_later']['Row']
type VideoLike = Database['public']['Tables']['video_likes']['Row']
type Video = Database['public']['Tables']['videos']['Row']

interface HistoryState {
  watchHistory: (WatchHistory & { videos: Video })[]
  watchLater: (WatchLater & { videos: Video })[]
  likedVideos: (VideoLike & { videos: Video })[]
  loading: boolean
  
  // Watch History
  addToHistory: (videoId: string, progress?: number) => Promise<void>
  fetchWatchHistory: (userId: string) => Promise<void>
  clearWatchHistory: (userId: string) => Promise<void>
  
  // Watch Later
  addToWatchLater: (videoId: string) => Promise<void>
  removeFromWatchLater: (videoId: string) => Promise<void>
  fetchWatchLater: (userId: string) => Promise<void>
  isInWatchLater: (videoId: string) => boolean
  
  // Video Likes
  likeVideo: (videoId: string) => Promise<void>
  dislikeVideo: (videoId: string) => Promise<void>
  removeLike: (videoId: string) => Promise<void>
  fetchLikedVideos: (userId: string) => Promise<void>
  getVideoLikeStatus: (videoId: string) => 'liked' | 'disliked' | null
}

export const useHistoryStore = create<HistoryState>((set, get) => ({
  watchHistory: [],
  watchLater: [],
  likedVideos: [],
  loading: false,

  // Watch History
  addToHistory: async (videoId: string, progress = 0) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // Check if already exists
      const { data: existing } = await supabase
        .from('watch_history')
        .select('id')
        .eq('user_id', user.id)
        .eq('video_id', videoId)
        .single()

      if (existing) {
        // Update existing record
        await supabase
          .from('watch_history')
          .update({
            watched_at: new Date().toISOString(),
            progress,
          })
          .eq('id', existing.id)
      } else {
        // Insert new record
        await supabase
          .from('watch_history')
          .insert({
            user_id: user.id,
            video_id: videoId,
            progress,
          })
      }
    } catch (error) {
      console.error('Error adding to history:', error)
    }
  },

  fetchWatchHistory: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('watch_history')
        .select(`
          *,
          videos (
            *,
            profiles:user_id (
              username,
              avatar_url
            )
          )
        `)
        .eq('user_id', userId)
        .order('watched_at', { ascending: false })
        .limit(50)

      if (error) throw error
      set({ watchHistory: data || [] })
    } catch (error) {
      console.error('Error fetching watch history:', error)
    } finally {
      set({ loading: false })
    }
  },

  clearWatchHistory: async (userId: string) => {
    try {
      const { error } = await supabase
        .from('watch_history')
        .delete()
        .eq('user_id', userId)

      if (error) throw error
      set({ watchHistory: [] })
    } catch (error) {
      console.error('Error clearing watch history:', error)
      throw error
    }
  },

  // Watch Later
  addToWatchLater: async (videoId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('watch_later')
        .insert({
          user_id: user.id,
          video_id: videoId,
        })

      if (error) throw error

      // Refresh watch later list
      get().fetchWatchLater(user.id)
    } catch (error) {
      console.error('Error adding to watch later:', error)
      throw error
    }
  },

  removeFromWatchLater: async (videoId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('watch_later')
        .delete()
        .eq('user_id', user.id)
        .eq('video_id', videoId)

      if (error) throw error

      // Update local state
      set(state => ({
        watchLater: state.watchLater.filter(item => item.video_id !== videoId)
      }))
    } catch (error) {
      console.error('Error removing from watch later:', error)
      throw error
    }
  },

  fetchWatchLater: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('watch_later')
        .select(`
          *,
          videos (
            *,
            profiles:user_id (
              username,
              avatar_url
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      set({ watchLater: data || [] })
    } catch (error) {
      console.error('Error fetching watch later:', error)
    } finally {
      set({ loading: false })
    }
  },

  isInWatchLater: (videoId: string) => {
    return get().watchLater.some(item => item.video_id === videoId)
  },

  // Video Likes
  likeVideo: async (videoId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Check if already liked/disliked
      const { data: existing } = await supabase
        .from('video_likes')
        .select('*')
        .eq('user_id', user.id)
        .eq('video_id', videoId)
        .single()

      if (existing) {
        if (existing.is_like) {
          // Already liked, remove like
          await supabase
            .from('video_likes')
            .delete()
            .eq('id', existing.id)
        } else {
          // Change dislike to like
          await supabase
            .from('video_likes')
            .update({ is_like: true })
            .eq('id', existing.id)
        }
      } else {
        // New like
        await supabase
          .from('video_likes')
          .insert({
            user_id: user.id,
            video_id: videoId,
            is_like: true,
          })
      }

      // Update video likes count
      await supabase.rpc('update_video_likes', { video_id: videoId })
    } catch (error) {
      console.error('Error liking video:', error)
      throw error
    }
  },

  dislikeVideo: async (videoId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Check if already liked/disliked
      const { data: existing } = await supabase
        .from('video_likes')
        .select('*')
        .eq('user_id', user.id)
        .eq('video_id', videoId)
        .single()

      if (existing) {
        if (!existing.is_like) {
          // Already disliked, remove dislike
          await supabase
            .from('video_likes')
            .delete()
            .eq('id', existing.id)
        } else {
          // Change like to dislike
          await supabase
            .from('video_likes')
            .update({ is_like: false })
            .eq('id', existing.id)
        }
      } else {
        // New dislike
        await supabase
          .from('video_likes')
          .insert({
            user_id: user.id,
            video_id: videoId,
            is_like: false,
          })
      }

      // Update video likes count
      await supabase.rpc('update_video_likes', { video_id: videoId })
    } catch (error) {
      console.error('Error disliking video:', error)
      throw error
    }
  },

  removeLike: async (videoId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('video_likes')
        .delete()
        .eq('user_id', user.id)
        .eq('video_id', videoId)

      if (error) throw error

      // Update video likes count
      await supabase.rpc('update_video_likes', { video_id: videoId })
    } catch (error) {
      console.error('Error removing like:', error)
      throw error
    }
  },

  fetchLikedVideos: async (userId: string) => {
    set({ loading: true })
    try {
      const { data, error } = await supabase
        .from('video_likes')
        .select(`
          *,
          videos (
            *,
            profiles:user_id (
              username,
              avatar_url
            )
          )
        `)
        .eq('user_id', userId)
        .eq('is_like', true)
        .order('created_at', { ascending: false })

      if (error) throw error
      set({ likedVideos: data || [] })
    } catch (error) {
      console.error('Error fetching liked videos:', error)
    } finally {
      set({ loading: false })
    }
  },

  getVideoLikeStatus: (videoId: string) => {
    const like = get().likedVideos.find(item => item.video_id === videoId)
    if (!like) return null
    return like.is_like ? 'liked' : 'disliked'
  },
}))
