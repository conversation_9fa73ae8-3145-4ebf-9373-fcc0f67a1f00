'use client'

import { useEffect, useState } from 'react'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'
import { History, Search, Trash2, Calendar } from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import Link from 'next/link'

export default function HistoryPage() {
  const { user } = useAuthStore()
  const { watchHistory, loading, fetchWatchHistory, clearWatchHistory } = useHistoryStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredHistory, setFilteredHistory] = useState(watchHistory)

  useEffect(() => {
    if (user) {
      fetchWatchHistory(user.id)
    }
  }, [user, fetchWatchHistory])

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = watchHistory.filter(item =>
        item.videos.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.videos as any).profiles?.username?.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredHistory(filtered)
    } else {
      setFilteredHistory(watchHistory)
    }
  }, [searchQuery, watchHistory])

  const handleClearHistory = async () => {
    if (!user) return
    
    if (confirm('Are you sure you want to clear your entire watch history? This action cannot be undone.')) {
      try {
        await clearWatchHistory(user.id)
      } catch (error) {
        console.error('Error clearing history:', error)
      }
    }
  }

  const groupHistoryByDate = (history: typeof watchHistory) => {
    const groups: { [key: string]: typeof history } = {}
    
    history.forEach(item => {
      const date = format(new Date(item.watched_at), 'yyyy-MM-dd')
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(item)
    })
    
    return groups
  }

  const formatDateGroup = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')) {
      return 'Today'
    } else if (format(date, 'yyyy-MM-dd') === format(yesterday, 'yyyy-MM-dd')) {
      return 'Yesterday'
    } else {
      return format(date, 'MMMM d, yyyy')
    }
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <History className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to view your history</h2>
          <p className="text-muted-foreground">
            Your watch history helps you find videos you've watched before and improves your recommendations.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Watch History</h1>
          <div className="h-4 w-32 bg-muted rounded animate-pulse mt-2" />
        </div>
        
        <div className="space-y-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i}>
              <div className="h-6 w-24 bg-muted rounded animate-pulse mb-4" />
              <div className="grid gap-4">
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j} className="flex space-x-4 animate-pulse">
                    <div className="w-40 aspect-video bg-muted rounded" />
                    <div className="flex-1">
                      <div className="h-4 bg-muted rounded mb-2" />
                      <div className="h-3 bg-muted rounded w-2/3" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const historyGroups = groupHistoryByDate(filteredHistory)

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <History className="w-6 h-6 mr-2 text-purple-600" />
            <h1 className="text-2xl font-bold">Watch History</h1>
          </div>
          
          {watchHistory.length > 0 && (
            <Button
              variant="outline"
              onClick={handleClearHistory}
              className="flex items-center text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Clear All History
            </Button>
          )}
        </div>
        
        {watchHistory.length > 0 && (
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search watch history..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        )}
      </div>

      {watchHistory.length === 0 ? (
        <div className="text-center py-12">
          <History className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No watch history</h2>
          <p className="text-muted-foreground mb-4">
            Videos you watch will appear here so you can easily find them again.
          </p>
          <Link href="/">
            <Button>Start Watching</Button>
          </Link>
        </div>
      ) : filteredHistory.length === 0 ? (
        <div className="text-center py-12">
          <Search className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No results found</h2>
          <p className="text-muted-foreground">
            Try searching with different keywords.
          </p>
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(historyGroups)
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
            .map(([date, items]) => (
              <div key={date}>
                <div className="flex items-center mb-4">
                  <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
                  <h2 className="text-lg font-semibold">{formatDateGroup(date)}</h2>
                  <span className="ml-2 text-sm text-muted-foreground">
                    ({items.length} video{items.length !== 1 ? 's' : ''})
                  </span>
                </div>
                
                <div className="grid gap-4">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-start space-x-4 p-4 bg-card rounded-lg border hover:bg-accent/50 transition-colors">
                      <Link href={`/watch/${item.video_id}`} className="flex-shrink-0">
                        <div className="relative w-40 aspect-video bg-muted rounded overflow-hidden">
                          {item.videos.thumbnail_url ? (
                            <img
                              src={item.videos.thumbnail_url}
                              alt={item.videos.title}
                              className="w-full h-full object-cover hover:scale-105 transition-transform"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                              <span className="text-white text-lg font-semibold">
                                {item.videos.title.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                          
                          {/* Progress bar if video was partially watched */}
                          {item.progress > 0 && item.videos.duration && (
                            <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20">
                              <div 
                                className="h-full bg-red-600"
                                style={{ 
                                  width: `${Math.min((item.progress / item.videos.duration) * 100, 100)}%` 
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </Link>
                      
                      <div className="flex-1 min-w-0">
                        <Link href={`/watch/${item.video_id}`}>
                          <h3 className="font-semibold text-sm line-clamp-2 hover:text-primary transition-colors mb-1">
                            {item.videos.title}
                          </h3>
                        </Link>
                        
                        <p className="text-xs text-muted-foreground mb-2">
                          {(item.videos as any).profiles?.username || 'Unknown User'}
                        </p>
                        
                        <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                          {item.videos.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-xs text-muted-foreground space-x-2">
                            <span>{item.videos.views.toLocaleString()} views</span>
                            <span>•</span>
                            <span>
                              Watched {formatDistanceToNow(new Date(item.watched_at), { addSuffix: true })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}
