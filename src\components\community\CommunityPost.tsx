'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { useAuthStore } from '@/store/authStore'
import { supabase } from '@/lib/supabase'
import { 
  ThumbsUp, 
  ThumbsDown, 
  MessageCircle, 
  Share, 
  MoreHorizontal,
  Image as ImageIcon,
  Video,
  Poll,
  Send
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import Link from 'next/link'

interface CommunityPostProps {
  post: {
    id: string
    content: string
    type: 'text' | 'image' | 'video' | 'poll'
    media_url?: string | null
    poll_options?: string[] | null
    likes: number
    dislikes: number
    comments_count: number
    user_id: string
    created_at: string
    profiles: {
      username: string
      avatar_url: string | null
      full_name: string | null
    }
  }
}

export function CommunityPost({ post }: CommunityPostProps) {
  const { user } = useAuthStore()
  const [showComments, setShowComments] = useState(false)
  const [newComment, setNewComment] = useState('')
  const [comments, setComments] = useState<any[]>([])
  const [loadingComments, setLoadingComments] = useState(false)

  const handleLike = async () => {
    if (!user) return
    
    try {
      // Implementation for liking community posts
      await supabase.rpc('toggle_community_post_like', {
        post_id: post.id,
        user_id: user.id,
        is_like: true
      })
    } catch (error) {
      console.error('Error liking post:', error)
    }
  }

  const handleDislike = async () => {
    if (!user) return
    
    try {
      await supabase.rpc('toggle_community_post_like', {
        post_id: post.id,
        user_id: user.id,
        is_like: false
      })
    } catch (error) {
      console.error('Error disliking post:', error)
    }
  }

  const handleShare = async () => {
    try {
      await navigator.share({
        title: `Post by ${post.profiles.username}`,
        text: post.content,
        url: `${window.location.origin}/community/${post.id}`,
      })
    } catch (error) {
      navigator.clipboard.writeText(`${window.location.origin}/community/${post.id}`)
      alert('Link copied to clipboard!')
    }
  }

  const loadComments = async () => {
    if (comments.length > 0) return
    
    setLoadingComments(true)
    try {
      const { data, error } = await supabase
        .from('community_comments')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('post_id', post.id)
        .order('created_at', { ascending: true })

      if (error) throw error
      setComments(data || [])
    } catch (error) {
      console.error('Error loading comments:', error)
    } finally {
      setLoadingComments(false)
    }
  }

  const handleToggleComments = () => {
    setShowComments(!showComments)
    if (!showComments) {
      loadComments()
    }
  }

  const handleSubmitComment = async () => {
    if (!user || !newComment.trim()) return

    try {
      const { data, error } = await supabase
        .from('community_comments')
        .insert({
          post_id: post.id,
          user_id: user.id,
          content: newComment.trim(),
        })
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .single()

      if (error) throw error
      
      setComments([...comments, data])
      setNewComment('')
    } catch (error) {
      console.error('Error submitting comment:', error)
    }
  }

  const getPostTypeIcon = () => {
    switch (post.type) {
      case 'image':
        return <ImageIcon className="w-4 h-4" />
      case 'video':
        return <Video className="w-4 h-4" />
      case 'poll':
        return <Poll className="w-4 h-4" />
      default:
        return null
    }
  }

  return (
    <Card className="mb-4">
      <CardContent className="p-6">
        {/* Post Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Link href={`/channel/${post.user_id}`}>
              <Avatar className="w-10 h-10">
                <AvatarImage src={post.profiles.avatar_url || ''} />
                <AvatarFallback>
                  {post.profiles.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </Link>
            
            <div>
              <div className="flex items-center space-x-2">
                <Link href={`/channel/${post.user_id}`}>
                  <h4 className="font-semibold hover:underline">
                    {post.profiles.full_name || post.profiles.username}
                  </h4>
                </Link>
                {getPostTypeIcon() && (
                  <Badge variant="secondary" className="text-xs">
                    {getPostTypeIcon()}
                    {post.type}
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                @{post.profiles.username} • {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>
          
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>

        {/* Post Content */}
        <div className="mb-4">
          <p className="whitespace-pre-wrap">{post.content}</p>
          
          {/* Media Content */}
          {post.media_url && (
            <div className="mt-3">
              {post.type === 'image' && (
                <img
                  src={post.media_url}
                  alt="Post image"
                  className="rounded-lg max-w-full h-auto"
                />
              )}
              {post.type === 'video' && (
                <video
                  src={post.media_url}
                  controls
                  className="rounded-lg max-w-full h-auto"
                />
              )}
            </div>
          )}
          
          {/* Poll */}
          {post.type === 'poll' && post.poll_options && (
            <div className="mt-3 space-y-2">
              {post.poll_options.map((option, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {/* Handle poll vote */}}
                >
                  {option}
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Post Actions */}
        <div className="flex items-center justify-between border-t pt-3">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={handleLike}>
              <ThumbsUp className="w-4 h-4 mr-1" />
              {post.likes}
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleDislike}>
              <ThumbsDown className="w-4 h-4 mr-1" />
              {post.dislikes}
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleToggleComments}>
              <MessageCircle className="w-4 h-4 mr-1" />
              {post.comments_count}
            </Button>
          </div>
          
          <Button variant="ghost" size="sm" onClick={handleShare}>
            <Share className="w-4 h-4 mr-1" />
            Share
          </Button>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div className="mt-4 border-t pt-4">
            {/* Add Comment */}
            {user && (
              <div className="flex space-x-3 mb-4">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user.user_metadata?.avatar_url} />
                  <AvatarFallback>
                    {user.user_metadata?.username?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="min-h-[60px]"
                  />
                  <div className="flex justify-end mt-2">
                    <Button
                      size="sm"
                      onClick={handleSubmitComment}
                      disabled={!newComment.trim()}
                    >
                      <Send className="w-3 h-3 mr-1" />
                      Comment
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Comments List */}
            {loadingComments ? (
              <div className="text-center py-4 text-muted-foreground">
                Loading comments...
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No comments yet. Be the first to comment!
              </div>
            ) : (
              <div className="space-y-3">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={comment.profiles.avatar_url || ''} />
                      <AvatarFallback>
                        {comment.profiles.username.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-sm">
                            {comment.profiles.username}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                          </span>
                        </div>
                        <p className="text-sm">{comment.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
