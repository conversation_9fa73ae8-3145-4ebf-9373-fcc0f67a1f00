'use client'

import { useEffect } from 'react'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { useHistoryStore } from '@/store/historyStore'
import { useAuthStore } from '@/store/authStore'
import { ThumbsUp, Play, Heart } from 'lucide-react'
import Link from 'next/link'

export default function LikedVideosPage() {
  const { user } = useAuthStore()
  const { likedVideos, loading, fetchLikedVideos } = useHistoryStore()

  useEffect(() => {
    if (user) {
      fetchLikedVideos(user.id)
    }
  }, [user, fetchLikedVideos])

  const handlePlayAll = () => {
    if (likedVideos.length > 0) {
      // Navigate to first video with playlist context
      window.location.href = `/watch/${likedVideos[0].video_id}?playlist=liked`
    }
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <ThumbsUp className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">Sign in to see liked videos</h2>
          <p className="text-muted-foreground">
            Videos you like will appear here so you can easily find them again.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Liked Videos</h1>
          <div className="h-4 w-32 bg-muted rounded animate-pulse mt-2" />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="aspect-video bg-muted rounded-lg mb-3" />
              <div className="flex space-x-3">
                <div className="w-9 h-9 bg-muted rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded mb-2" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <ThumbsUp className="w-6 h-6 mr-2 text-red-600" />
            <h1 className="text-2xl font-bold">Liked Videos</h1>
          </div>
          
          {likedVideos.length > 0 && (
            <Button onClick={handlePlayAll} className="flex items-center">
              <Play className="w-4 h-4 mr-1" />
              Play All
            </Button>
          )}
        </div>
        
        <p className="text-muted-foreground">
          {likedVideos.length} video{likedVideos.length !== 1 ? 's' : ''} liked
        </p>
      </div>

      {likedVideos.length === 0 ? (
        <div className="text-center py-12">
          <div className="relative mb-4">
            <Heart className="w-16 h-16 mx-auto text-muted-foreground" />
            <ThumbsUp className="w-8 h-8 absolute -top-1 -right-1 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold mb-2">No liked videos yet</h2>
          <p className="text-muted-foreground mb-4">
            Videos you like will appear here. Start exploring and like videos you enjoy!
          </p>
          <Link href="/">
            <Button>Discover Videos</Button>
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Grid View for Liked Videos */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {likedVideos.map((item) => (
              <div key={item.id} className="relative group">
                {/* Like indicator */}
                <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="bg-red-600 text-white p-1 rounded-full">
                    <ThumbsUp className="w-3 h-3" />
                  </div>
                </div>
                
                <VideoCard video={item.videos} />
                
                {/* Additional metadata for liked videos */}
                <div className="mt-2 px-2">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Liked {new Date(item.created_at).toLocaleDateString()}</span>
                    <div className="flex items-center">
                      <ThumbsUp className="w-3 h-3 mr-1" />
                      <span>{item.videos.likes}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
