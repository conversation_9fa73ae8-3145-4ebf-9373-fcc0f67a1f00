{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      videos: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          video_url: string\n          thumbnail_url: string | null\n          duration: number | null\n          views: number\n          likes: number\n          dislikes: number\n          user_id: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          video_url: string\n          thumbnail_url?: string | null\n          duration?: number | null\n          views?: number\n          likes?: number\n          dislikes?: number\n          user_id: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          video_url?: string\n          thumbnail_url?: string | null\n          duration?: number | null\n          views?: number\n          likes?: number\n          dislikes?: number\n          user_id?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      profiles: {\n        Row: {\n          id: string\n          username: string\n          full_name: string | null\n          avatar_url: string | null\n          bio: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          username: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          username?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      comments: {\n        Row: {\n          id: string\n          video_id: string\n          user_id: string\n          content: string\n          likes: number\n          parent_id: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          video_id: string\n          user_id: string\n          content: string\n          likes?: number\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          video_id?: string\n          user_id?: string\n          content?: string\n          likes?: number\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,6BAA6B;AAE1D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa"}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthState {\n  user: User | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<void>\n  signUp: (email: string, password: string, username: string) => Promise<void>\n  signOut: () => Promise<void>\n  initialize: () => Promise<void>\n}\n\nexport const useAuthStore = create<AuthState>((set, get) => ({\n  user: null,\n  loading: true,\n\n  signIn: async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    \n    if (error) throw error\n    set({ user: data.user })\n  },\n\n  signUp: async (email: string, password: string, username: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          username,\n        },\n      },\n    })\n    \n    if (error) throw error\n    \n    // Create profile\n    if (data.user) {\n      const { error: profileError } = await supabase\n        .from('profiles')\n        .insert({\n          id: data.user.id,\n          username,\n          full_name: username,\n        })\n      \n      if (profileError) throw profileError\n    }\n    \n    set({ user: data.user })\n  },\n\n  signOut: async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n    set({ user: null })\n  },\n\n  initialize: async () => {\n    const { data: { session } } = await supabase.auth.getSession()\n    set({ user: session?.user ?? null, loading: false })\n\n    supabase.auth.onAuthStateChange((event, session) => {\n      set({ user: session?.user ?? null })\n    })\n  },\n}))\n"], "names": [], "mappings": ";;;AAEA;AAFA;;;AAaO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,MAAM;QACN,SAAS;QAET,QAAQ,OAAO,OAAe;YAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YACjB,IAAI;gBAAE,MAAM,KAAK,IAAI;YAAC;QACxB;QAEA,QAAQ,OAAO,OAAe,UAAkB;YAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ;oBACF;gBACF;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,iBAAiB;YACjB,IAAI,KAAK,IAAI,EAAE;gBACb,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB;oBACA,WAAW;gBACb;gBAEF,IAAI,cAAc,MAAM;YAC1B;YAEA,IAAI;gBAAE,MAAM,KAAK,IAAI;YAAC;QACxB;QAEA,SAAS;YACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,IAAI;gBAAE,MAAM;YAAK;QACnB;QAEA,YAAY;YACV,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,IAAI;gBAAE,MAAM,SAAS,QAAQ;gBAAM,SAAS;YAAM;YAElD,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO;gBACtC,IAAI;oBAAE,MAAM,SAAS,QAAQ;gBAAK;YACpC;QACF;IACF,CAAC"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/store/videoStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { supabase, Database } from '@/lib/supabase'\n\ntype Video = Database['public']['Tables']['videos']['Row']\n\ninterface VideoState {\n  videos: Video[]\n  currentVideo: Video | null\n  loading: boolean\n  searchQuery: string\n  fetchVideos: () => Promise<void>\n  fetchVideoById: (id: string) => Promise<void>\n  uploadVideo: (videoData: {\n    title: string\n    description?: string\n    videoFile: File\n    thumbnailFile?: File\n  }) => Promise<void>\n  searchVideos: (query: string) => Promise<void>\n  incrementViews: (videoId: string) => Promise<void>\n  likeVideo: (videoId: string) => Promise<void>\n  setSearchQuery: (query: string) => void\n}\n\nexport const useVideoStore = create<VideoState>((set, get) => ({\n  videos: [],\n  currentVideo: null,\n  loading: false,\n  searchQuery: '',\n\n  fetchVideos: async () => {\n    set({ loading: true })\n    try {\n      const { data, error } = await supabase\n        .from('videos')\n        .select(`\n          *,\n          profiles:user_id (\n            username,\n            avatar_url\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      set({ videos: data || [] })\n    } catch (error) {\n      console.error('Error fetching videos:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  fetchVideoById: async (id: string) => {\n    set({ loading: true })\n    try {\n      const { data, error } = await supabase\n        .from('videos')\n        .select(`\n          *,\n          profiles:user_id (\n            username,\n            avatar_url,\n            full_name\n          )\n        `)\n        .eq('id', id)\n        .single()\n\n      if (error) throw error\n      set({ currentVideo: data })\n    } catch (error) {\n      console.error('Error fetching video:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  uploadVideo: async (videoData) => {\n    set({ loading: true })\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n\n      // Upload video file\n      const videoFileName = `${Date.now()}-${videoData.videoFile.name}`\n      const { data: videoUpload, error: videoError } = await supabase.storage\n        .from('videos')\n        .upload(videoFileName, videoData.videoFile)\n\n      if (videoError) throw videoError\n\n      // Upload thumbnail if provided\n      let thumbnailUrl = null\n      if (videoData.thumbnailFile) {\n        const thumbnailFileName = `${Date.now()}-${videoData.thumbnailFile.name}`\n        const { data: thumbnailUpload, error: thumbnailError } = await supabase.storage\n          .from('thumbnails')\n          .upload(thumbnailFileName, videoData.thumbnailFile)\n\n        if (thumbnailError) throw thumbnailError\n\n        const { data: thumbnailUrlData } = supabase.storage\n          .from('thumbnails')\n          .getPublicUrl(thumbnailUpload.path)\n        \n        thumbnailUrl = thumbnailUrlData.publicUrl\n      }\n\n      // Get video URL\n      const { data: videoUrlData } = supabase.storage\n        .from('videos')\n        .getPublicUrl(videoUpload.path)\n\n      // Insert video record\n      const { data, error } = await supabase\n        .from('videos')\n        .insert({\n          title: videoData.title,\n          description: videoData.description,\n          video_url: videoUrlData.publicUrl,\n          thumbnail_url: thumbnailUrl,\n          user_id: user.id,\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n\n      // Refresh videos list\n      get().fetchVideos()\n    } catch (error) {\n      console.error('Error uploading video:', error)\n      throw error\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  searchVideos: async (query: string) => {\n    set({ loading: true, searchQuery: query })\n    try {\n      const { data, error } = await supabase\n        .from('videos')\n        .select(`\n          *,\n          profiles:user_id (\n            username,\n            avatar_url\n          )\n        `)\n        .ilike('title', `%${query}%`)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      set({ videos: data || [] })\n    } catch (error) {\n      console.error('Error searching videos:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  incrementViews: async (videoId: string) => {\n    try {\n      const { error } = await supabase.rpc('increment_views', {\n        video_id: videoId\n      })\n      \n      if (error) throw error\n    } catch (error) {\n      console.error('Error incrementing views:', error)\n    }\n  },\n\n  likeVideo: async (videoId: string) => {\n    try {\n      const { error } = await supabase.rpc('increment_likes', {\n        video_id: videoId\n      })\n      \n      if (error) throw error\n    } catch (error) {\n      console.error('Error liking video:', error)\n    }\n  },\n\n  setSearchQuery: (query: string) => {\n    set({ searchQuery: query })\n  },\n}))\n"], "names": [], "mappings": ";;;AACA;AADA;;;AAwBO,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,QAAQ,EAAE;QACV,cAAc;QACd,SAAS;QACT,aAAa;QAEb,aAAa;YACX,IAAI;gBAAE,SAAS;YAAK;YACpB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBACjB,IAAI;oBAAE,QAAQ,QAAQ,EAAE;gBAAC;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBAAE,SAAS;YAAK;YACpB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;QAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;gBAET,IAAI,OAAO,MAAM;gBACjB,IAAI;oBAAE,cAAc;gBAAK;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO;YAClB,IAAI;gBAAE,SAAS;YAAK;YACpB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,oBAAoB;gBACpB,MAAM,gBAAgB,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,UAAU,SAAS,CAAC,IAAI,EAAE;gBACjE,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CACpE,IAAI,CAAC,UACL,MAAM,CAAC,eAAe,UAAU,SAAS;gBAE5C,IAAI,YAAY,MAAM;gBAEtB,+BAA+B;gBAC/B,IAAI,eAAe;gBACnB,IAAI,UAAU,aAAa,EAAE;oBAC3B,MAAM,oBAAoB,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,UAAU,aAAa,CAAC,IAAI,EAAE;oBACzE,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC5E,IAAI,CAAC,cACL,MAAM,CAAC,mBAAmB,UAAU,aAAa;oBAEpD,IAAI,gBAAgB,MAAM;oBAE1B,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAChD,IAAI,CAAC,cACL,YAAY,CAAC,gBAAgB,IAAI;oBAEpC,eAAe,iBAAiB,SAAS;gBAC3C;gBAEA,gBAAgB;gBAChB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC5C,IAAI,CAAC,UACL,YAAY,CAAC,YAAY,IAAI;gBAEhC,sBAAsB;gBACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC;oBACN,OAAO,UAAU,KAAK;oBACtB,aAAa,UAAU,WAAW;oBAClC,WAAW,aAAa,SAAS;oBACjC,eAAe;oBACf,SAAS,KAAK,EAAE;gBAClB,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,sBAAsB;gBACtB,MAAM,WAAW;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OAAO;YACnB,IAAI;gBAAE,SAAS;gBAAM,aAAa;YAAM;YACxC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAC3B,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBACjB,IAAI;oBAAE,QAAQ,QAAQ,EAAE;gBAAC;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,mBAAmB;oBACtD,UAAU;gBACZ;gBAEA,IAAI,OAAO,MAAM;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA,WAAW,OAAO;YAChB,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,mBAAmB;oBACtD,UAAU;gBACZ;gBAEA,IAAI,OAAO,MAAM;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACvC;QACF;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAM;QAC3B;IACF,CAAC"}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuthStore } from '@/store/authStore'\nimport { X } from 'lucide-react'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function AuthModal({ isOpen, onClose }: AuthModalProps) {\n  const [isSignUp, setIsSignUp] = useState(false)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [username, setUsername] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuthStore()\n\n  if (!isOpen) return null\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      if (isSignUp) {\n        await signUp(email, password, username)\n      } else {\n        await signIn(email, password)\n      }\n      onClose()\n      // Reset form\n      setEmail('')\n      setPassword('')\n      setUsername('')\n    } catch (error: any) {\n      setError(error.message || 'An error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <Card className=\"w-full max-w-md mx-4\">\n        <CardHeader className=\"relative\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"absolute right-0 top-0\"\n            onClick={onClose}\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n          <CardTitle>{isSignUp ? 'Sign Up' : 'Sign In'}</CardTitle>\n          <CardDescription>\n            {isSignUp \n              ? 'Create an account to start uploading videos' \n              : 'Sign in to your account'\n            }\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {isSignUp && (\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-medium mb-1\">\n                  Username\n                </label>\n                <Input\n                  id=\"username\"\n                  type=\"text\"\n                  value={username}\n                  onChange={(e) => setUsername(e.target.value)}\n                  required\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n            )}\n            \n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium mb-1\">\n                Email\n              </label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                placeholder=\"Enter your email\"\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium mb-1\">\n                Password\n              </label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                placeholder=\"Enter your password\"\n                minLength={6}\n              />\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm\">{error}</div>\n            )}\n\n            <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n              {loading ? 'Loading...' : (isSignUp ? 'Sign Up' : 'Sign In')}\n            </Button>\n          </form>\n\n          <div className=\"mt-4 text-center\">\n            <Button\n              variant=\"link\"\n              onClick={() => setIsSignUp(!isSignUp)}\n              className=\"text-sm\"\n            >\n              {isSignUp \n                ? 'Already have an account? Sign in' \n                : \"Don't have an account? Sign up\"\n              }\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAcO,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEtC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,OAAO,OAAO,UAAU;YAChC,OAAO;gBACL,MAAM,OAAO,OAAO;YACtB;YACA;YACA,aAAa;YACb,SAAS;YACT,YAAY;YACZ,YAAY;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,8OAAC,gIAAA,CAAA,YAAS;sCAAE,WAAW,YAAY;;;;;;sCACnC,8OAAC,gIAAA,CAAA,kBAAe;sCACb,WACG,gDACA;;;;;;;;;;;;8BAIR,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,0BACC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiC;;;;;;sDAGrE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,aAAY;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAiC;;;;;;sDAGlE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiC;;;;;;sDAGrE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,aAAY;4CACZ,WAAW;;;;;;;;;;;;gCAId,uBACC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;8CAGzC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,WAAU;oCAAS,UAAU;8CAChD,UAAU,eAAgB,WAAW,YAAY;;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WACG,qCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB"}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Upload, User, LogOut, Menu } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { useAuthStore } from '@/store/authStore'\nimport { useVideoStore } from '@/store/videoStore'\nimport { AuthModal } from '@/components/auth/AuthModal'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\n\nexport function Header() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [showAuthModal, setShowAuthModal] = useState(false)\n  const { user, signOut } = useAuthStore()\n  const { searchVideos, setSearchQuery: setStoreSearchQuery } = useVideoStore()\n  const router = useRouter()\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      setStoreSearchQuery(searchQuery)\n      await searchVideos(searchQuery)\n      router.push('/search')\n    }\n  }\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  return (\n    <>\n      <header className=\"flex items-center justify-between px-4 py-3 border-b bg-background\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-red-600 rounded flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">YT</span>\n            </div>\n            <span className=\"font-bold text-xl hidden sm:block\">YTClone</span>\n          </Link>\n        </div>\n\n        <form onSubmit={handleSearch} className=\"flex-1 max-w-2xl mx-4\">\n          <div className=\"flex\">\n            <Input\n              type=\"text\"\n              placeholder=\"Search videos...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"rounded-r-none border-r-0\"\n            />\n            <Button type=\"submit\" variant=\"outline\" className=\"rounded-l-none\">\n              <Search className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </form>\n\n        <div className=\"flex items-center space-x-2\">\n          {user ? (\n            <>\n              <Link href=\"/upload\">\n                <Button variant=\"ghost\" size=\"icon\">\n                  <Upload className=\"h-5 w-5\" />\n                </Button>\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <Avatar className=\"h-8 w-8\">\n                  <AvatarImage src={user.user_metadata?.avatar_url} />\n                  <AvatarFallback>\n                    <User className=\"h-4 w-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <Button variant=\"ghost\" size=\"icon\" onClick={handleSignOut}>\n                  <LogOut className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </>\n          ) : (\n            <Button onClick={() => setShowAuthModal(true)}>\n              Sign In\n            </Button>\n          )}\n        </div>\n      </header>\n\n      <AuthModal \n        isOpen={showAuthModal} \n        onClose={() => setShowAuthModal(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;;;AAaO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,YAAY,EAAE,gBAAgB,mBAAmB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAC1E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,oBAAoB;YACpB,MAAM,aAAa;YACnB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAU;0CAC5C,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;kCAIxD,8OAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,WAAU;8CAChD,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKxB,8OAAC;wBAAI,WAAU;kCACZ,qBACC;;8CACE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,aAAa,EAAE;;;;;;8DACtC,8OAAC,kIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,SAAS;sDAC3C,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;yDAKxB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,iBAAiB;sCAAO;;;;;;;;;;;;;;;;;0BAOrD,8OAAC,uIAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC"}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Home, TrendingUp, Clock, ThumbsUp, History, PlaySquare } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\nconst sidebarItems = [\n  { icon: Home, label: 'Home', href: '/' },\n  { icon: TrendingUp, label: 'Trending', href: '/trending' },\n  { icon: Clock, label: 'Watch Later', href: '/watch-later' },\n  { icon: ThumbsUp, label: 'Liked Videos', href: '/liked' },\n  { icon: History, label: 'History', href: '/history' },\n  { icon: PlaySquare, label: 'Your Videos', href: '/my-videos' },\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n\n  return (\n    <aside className=\"w-64 bg-background border-r hidden md:block\">\n      <nav className=\"p-4 space-y-2\">\n        {sidebarItems.map((item) => {\n          const Icon = item.icon\n          const isActive = pathname === item.href\n\n          return (\n            <Link key={item.href} href={item.href}>\n              <Button\n                variant={isActive ? 'secondary' : 'ghost'}\n                className={cn(\n                  'w-full justify-start',\n                  isActive && 'bg-secondary'\n                )}\n              >\n                <Icon className=\"mr-3 h-4 w-4\" />\n                {item.label}\n              </Button>\n            </Link>\n          )\n        })}\n      </nav>\n    </aside>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;;;;;AAQA,MAAM,eAAe;IACnB;QAAE,MAAM,mMAAA,CAAA,OAAI;QAAE,OAAO;QAAQ,MAAM;IAAI;IACvC;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;QAAe,MAAM;IAAe;IAC1D;QAAE,MAAM,8MAAA,CAAA,WAAQ;QAAE,OAAO;QAAgB,MAAM;IAAS;IACxD;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,OAAO;QAAW,MAAM;IAAW;IACpD;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;QAAe,MAAM;IAAa;CAC9D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAM,WAAU;kBACf,cAAA,8OAAC;YAAI,WAAU;sBACZ,aAAa,GAAG,CAAC,CAAC;gBACjB,MAAM,OAAO,KAAK,IAAI;gBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oBAAiB,MAAM,KAAK,IAAI;8BACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,WAAW,cAAc;wBAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wBACA,YAAY;;0CAGd,8OAAC;gCAAK,WAAU;;;;;;4BACf,KAAK,KAAK;;;;;;;mBATJ,KAAK,IAAI;;;;;YAaxB;;;;;;;;;;;AAIR"}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/ytaug/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useAuthStore } from '@/store/authStore'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const initialize = useAuthStore((state) => state.initialize)\n\n  useEffect(() => {\n    initialize()\n  }, [initialize])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,aAAa,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}