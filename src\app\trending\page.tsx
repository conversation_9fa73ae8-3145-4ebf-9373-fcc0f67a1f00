'use client'

import { useEffect, useState } from 'react'
import { VideoCard } from '@/components/video/VideoCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { supabase } from '@/lib/supabase'
import { TrendingUp, Calendar, Clock, Eye } from 'lucide-react'

interface TrendingVideo {
  id: string
  title: string
  description: string | null
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  views: number
  likes: number
  dislikes: number
  user_id: string
  category: string | null
  created_at: string
  profiles?: {
    username: string
    avatar_url: string | null
  }
}

type TrendingPeriod = 'today' | 'week' | 'month' | 'all'
type TrendingCategory = 'all' | 'music' | 'gaming' | 'sports' | 'news' | 'entertainment' | 'education'

export default function TrendingPage() {
  const [videos, setVideos] = useState<TrendingVideo[]>([])
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState<TrendingPeriod>('today')
  const [category, setCategory] = useState<TrendingCategory>('all')

  const periods = [
    { value: 'today' as TrendingPeriod, label: 'Today', icon: Calendar },
    { value: 'week' as TrendingPeriod, label: 'This Week', icon: Calendar },
    { value: 'month' as TrendingPeriod, label: 'This Month', icon: Calendar },
    { value: 'all' as TrendingPeriod, label: 'All Time', icon: TrendingUp },
  ]

  const categories = [
    { value: 'all' as TrendingCategory, label: 'All Categories' },
    { value: 'music' as TrendingCategory, label: 'Music' },
    { value: 'gaming' as TrendingCategory, label: 'Gaming' },
    { value: 'sports' as TrendingCategory, label: 'Sports' },
    { value: 'news' as TrendingCategory, label: 'News' },
    { value: 'entertainment' as TrendingCategory, label: 'Entertainment' },
    { value: 'education' as TrendingCategory, label: 'Education' },
  ]

  const fetchTrendingVideos = async () => {
    setLoading(true)
    try {
      let query = supabase
        .from('videos')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_public', true)

      // Apply category filter
      if (category !== 'all') {
        query = query.eq('category', category)
      }

      // Apply time period filter
      if (period !== 'all') {
        const now = new Date()
        let startDate: Date

        switch (period) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            break
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            break
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1)
            break
          default:
            startDate = new Date(0)
        }

        query = query.gte('created_at', startDate.toISOString())
      }

      // Order by trending score (views + likes - dislikes)
      const { data, error } = await query
        .order('views', { ascending: false })
        .limit(50)

      if (error) throw error

      // Calculate trending score and sort
      const trendingVideos = (data || [])
        .map(video => ({
          ...video,
          trendingScore: video.views + (video.likes * 2) - video.dislikes
        }))
        .sort((a, b) => b.trendingScore - a.trendingScore)

      setVideos(trendingVideos)
    } catch (error) {
      console.error('Error fetching trending videos:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTrendingVideos()
  }, [period, category])

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Trending</h1>
          <div className="flex flex-wrap gap-2 mb-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-8 w-20 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="aspect-video bg-muted rounded-lg mb-3" />
              <div className="flex space-x-3">
                <div className="w-9 h-9 bg-muted rounded-full flex-shrink-0" />
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded mb-2" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <TrendingUp className="w-6 h-6 mr-2 text-red-600" />
          <h1 className="text-2xl font-bold">Trending</h1>
        </div>
        
        {/* Time Period Filter */}
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Time Period</h3>
          <div className="flex flex-wrap gap-2">
            {periods.map((p) => {
              const Icon = p.icon
              return (
                <Button
                  key={p.value}
                  variant={period === p.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPeriod(p.value)}
                  className="flex items-center"
                >
                  <Icon className="w-4 h-4 mr-1" />
                  {p.label}
                </Button>
              )
            })}
          </div>
        </div>

        {/* Category Filter */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-2">Category</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((c) => (
              <Badge
                key={c.value}
                variant={category === c.value ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-primary/10"
                onClick={() => setCategory(c.value)}
              >
                {c.label}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {videos.length === 0 ? (
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">No trending videos</h2>
          <p className="text-muted-foreground">
            No videos are trending for the selected period and category.
          </p>
        </div>
      ) : (
        <>
          <div className="mb-4 flex items-center justify-between">
            <p className="text-muted-foreground">
              {videos.length} trending videos
            </p>
            <div className="flex items-center text-sm text-muted-foreground">
              <Eye className="w-4 h-4 mr-1" />
              Sorted by popularity
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {videos.map((video, index) => (
              <div key={video.id} className="relative">
                <div className="absolute top-2 left-2 z-10">
                  <Badge variant="secondary" className="bg-red-600 text-white">
                    #{index + 1}
                  </Badge>
                </div>
                <VideoCard video={video} />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  )
}
