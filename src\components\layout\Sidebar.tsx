'use client'

import { Home, TrendingUp, Clock, ThumbsUp, History, PlaySquare, Radio, Zap, Users, BarChart3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const sidebarItems = [
  { icon: Home, label: 'Home', href: '/' },
  { icon: TrendingUp, label: 'Trending', href: '/trending' },
  { icon: Zap, label: 'Shorts', href: '/shorts' },
  { icon: Radio, label: 'Live', href: '/live' },
  { icon: Clock, label: 'Watch Later', href: '/watch-later' },
  { icon: ThumbsUp, label: 'Liked Videos', href: '/liked' },
  { icon: History, label: 'History', href: '/history' },
  { icon: PlaySquare, label: 'Your Videos', href: '/my-videos' },
  { icon: BarChart3, label: 'Analytics', href: '/analytics' },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <aside className="w-64 bg-background border-r hidden md:block">
      <nav className="p-4 space-y-2">
        {sidebarItems.map((item) => {
          const Icon = item.icon
          const isActive = pathname === item.href

          return (
            <Link key={item.href} href={item.href}>
              <Button
                variant={isActive ? 'secondary' : 'ghost'}
                className={cn(
                  'w-full justify-start',
                  isActive && 'bg-secondary'
                )}
              >
                <Icon className="mr-3 h-4 w-4" />
                {item.label}
              </Button>
            </Link>
          )
        })}
      </nav>
    </aside>
  )
}
