'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Clock, Play, List } from 'lucide-react'

interface Chapter {
  id: string
  title: string
  start_time: number
  end_time?: number
  thumbnail_url?: string
}

interface VideoChaptersProps {
  videoId: string
  chapters: Chapter[]
  currentTime: number
  onSeekTo: (time: number) => void
  className?: string
}

export function VideoChapters({ 
  videoId, 
  chapters, 
  currentTime, 
  onSeekTo, 
  className 
}: VideoChaptersProps) {
  const [currentChapter, setCurrentChapter] = useState<Chapter | null>(null)
  const [showChaptersList, setShowChaptersList] = useState(false)

  useEffect(() => {
    // Find current chapter based on current time
    const current = chapters.find((chapter, index) => {
      const nextChapter = chapters[index + 1]
      return currentTime >= chapter.start_time && 
             (!nextChapter || currentTime < nextChapter.start_time)
    })
    
    setCurrentChapter(current || null)
  }, [currentTime, chapters])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const getChapterProgress = (chapter: Chapter) => {
    if (!currentChapter || currentChapter.id !== chapter.id) return 0
    
    const chapterDuration = (chapter.end_time || 0) - chapter.start_time
    const elapsed = currentTime - chapter.start_time
    
    return Math.min(100, Math.max(0, (elapsed / chapterDuration) * 100))
  }

  if (chapters.length === 0) return null

  return (
    <div className={className}>
      {/* Current Chapter Display */}
      {currentChapter && (
        <div className="mb-4 p-3 bg-accent rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">{currentChapter.title}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowChaptersList(!showChaptersList)}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Progress bar for current chapter */}
          <div className="mt-2">
            <div className="w-full bg-muted rounded-full h-1">
              <div
                className="bg-primary h-1 rounded-full transition-all duration-300"
                style={{ width: `${getChapterProgress(currentChapter)}%` }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Chapters List */}
      {showChaptersList && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <List className="w-5 h-5 mr-2" />
              Chapters
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-64 overflow-y-auto">
              {chapters.map((chapter, index) => {
                const isActive = currentChapter?.id === chapter.id
                const nextChapter = chapters[index + 1]
                const duration = nextChapter 
                  ? nextChapter.start_time - chapter.start_time
                  : undefined

                return (
                  <div
                    key={chapter.id}
                    className={`flex items-center p-3 hover:bg-accent cursor-pointer transition-colors border-b last:border-b-0 ${
                      isActive ? 'bg-accent' : ''
                    }`}
                    onClick={() => onSeekTo(chapter.start_time)}
                  >
                    {/* Chapter Thumbnail */}
                    <div className="relative w-20 h-12 bg-muted rounded mr-3 flex-shrink-0 overflow-hidden">
                      {chapter.thumbnail_url ? (
                        <img
                          src={chapter.thumbnail_url}
                          alt={chapter.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <Play className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      {/* Chapter number overlay */}
                      <div className="absolute top-1 left-1">
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          {index + 1}
                        </Badge>
                      </div>
                      
                      {/* Duration overlay */}
                      {duration && (
                        <div className="absolute bottom-1 right-1">
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {formatTime(duration)}
                          </Badge>
                        </div>
                      )}
                    </div>

                    {/* Chapter Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium text-primary">
                          {formatTime(chapter.start_time)}
                        </span>
                        {isActive && (
                          <Badge variant="default" className="text-xs">
                            Playing
                          </Badge>
                        )}
                      </div>
                      
                      <h4 className={`text-sm line-clamp-2 ${
                        isActive ? 'font-semibold' : 'font-medium'
                      }`}>
                        {chapter.title}
                      </h4>
                    </div>

                    {/* Play button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapter Navigation Timeline */}
      <div className="mt-4">
        <div className="relative">
          <div className="flex space-x-1 overflow-x-auto pb-2">
            {chapters.map((chapter, index) => {
              const isActive = currentChapter?.id === chapter.id
              const nextChapter = chapters[index + 1]
              const duration = nextChapter 
                ? nextChapter.start_time - chapter.start_time
                : 60 // Default duration for last chapter

              return (
                <Button
                  key={chapter.id}
                  variant={isActive ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onSeekTo(chapter.start_time)}
                  className="flex-shrink-0 text-xs"
                  style={{ minWidth: `${Math.max(60, duration * 2)}px` }}
                >
                  <div className="text-center">
                    <div className="font-medium">{formatTime(chapter.start_time)}</div>
                    <div className="text-xs opacity-75 line-clamp-1">
                      {chapter.title}
                    </div>
                  </div>
                </Button>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
