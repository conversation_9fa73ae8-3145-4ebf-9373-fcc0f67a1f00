<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>videojs-contrib-quality-levels Demo</title>
  <link href="/node_modules/video.js/dist/video-js.css" rel="stylesheet">
  <style>
    button.enabled {
      background: SkyBlue;
    }
    button.selected {
      background: SpringGreen;
    }
    button.disabled {
      background: red;
    }
  </style>
</head>
<body>
  <div id="fixture">
  </div>
  <div id="quality-levels">
    <h2>Quality Levels:</h2>
  </div>
  <ul>
    <li><a href="/test/debug.html">Run unit tests in browser.</a></li>
    <li><a href="/docs/api/">Read generated docs.</a></li>
  </ul>
  <script src="/node_modules/video.js/dist/video.js"></script>
  <script src="/node_modules/videojs-contrib-hls/dist/videojs-contrib-hls.js"></script>
  <script src="/dist/videojs-contrib-quality-levels.js"></script>
  <script>
    function createQualityButton(qualityLevel, parent) {
      var button = document.createElement('button');
      var classes = button.classList;

      if (qualityLevel.enabled) {
        classes.add('enabled');
      } else {
        classes.add('disabled');
      }

      button.innerHTML = qualityLevel.id + ': ' + qualityLevel.bitrate + ' kbps';
      button.id = 'quality-level-' + qualityLevel.id;

      button.onclick = function() {
        var old = qualityLevel.enabled;
        qualityLevel.enabled = !old;
        button.classList.toggle('enabled');
        button.classList.toggle('disabled');
      }
      parent.appendChild(button);
    }

    function createPlayer(callback) {
      var video = document.createElement('video');
      video.id = 'videojs-contrib-quality-levels-player';
      video.className = 'video-js vjs-default-skin';
      video.setAttribute('controls', true);
      video.setAttribute('height', 300);
      video.setAttribute('width', 600);
      document.querySelector('#fixture').appendChild(video);

      var options = {
        autoplay: false,
        qualityLevels: {}
      };
      var url = 'https://hslsslak-a.akamaihd.net/3303963094001/3303963094001_5147618278001_5147609827001.m3u8?pubId=3303963094001&videoId=5147609827001';
      var type = 'application/x-mpegURL';

      try {
        window.player = videojs(video.id, options);

        window.player.src({
          src: url,
          type: type
        });

        callback(window.player);
      } catch(err) {
        console.log("caught an error trying to create and add src to player:", err);
      }
    }
    function setup(player) {
      player.ready(function() {
        var qualityLevels = player.qualityLevels();
        var container = document.getElementById('quality-levels');

        qualityLevels.on('addqualitylevel', function(event) {
          createQualityButton(event.qualityLevel, container);
        });

        qualityLevels.on('change', function(event) {
          for (var i = 0; i < qualityLevels.length; i++) {
            var level = qualityLevels[i];
            var button = document.getElementById('quality-level-' + level.id);

            button.classList.remove('selected');
          }

          var selected = qualityLevels[event.selectedIndex];
          var button = document.getElementById('quality-level-' + selected.id);
          button.classList.add('selected');
        })
      });
    }

    (function(window, videojs) {
      createPlayer(setup);
    })(window, window.videojs);
  </script>
</body>
</html>
