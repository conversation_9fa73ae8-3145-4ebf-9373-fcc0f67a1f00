import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      videos: {
        Row: {
          id: string
          title: string
          description: string | null
          video_url: string
          thumbnail_url: string | null
          duration: number | null
          views: number
          likes: number
          dislikes: number
          user_id: string
          category: string | null
          tags: string[] | null
          is_public: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          video_url: string
          thumbnail_url?: string | null
          duration?: number | null
          views?: number
          likes?: number
          dislikes?: number
          user_id: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          video_url?: string
          thumbnail_url?: string | null
          duration?: number | null
          views?: number
          likes?: number
          dislikes?: number
          user_id?: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string | null
          avatar_url: string | null
          bio: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          video_id: string
          user_id: string
          content: string
          likes: number
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          video_id: string
          user_id: string
          content: string
          likes?: number
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          user_id?: string
          content?: string
          likes?: number
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          subscriber_id: string
          channel_id: string
          created_at: string
        }
        Insert: {
          id?: string
          subscriber_id: string
          channel_id: string
          created_at?: string
        }
        Update: {
          id?: string
          subscriber_id?: string
          channel_id?: string
          created_at?: string
        }
      }
      playlists: {
        Row: {
          id: string
          title: string
          description: string | null
          user_id: string
          is_public: boolean
          thumbnail_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          user_id: string
          is_public?: boolean
          thumbnail_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          user_id?: string
          is_public?: boolean
          thumbnail_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      playlist_videos: {
        Row: {
          id: string
          playlist_id: string
          video_id: string
          position: number
          created_at: string
        }
        Insert: {
          id?: string
          playlist_id: string
          video_id: string
          position?: number
          created_at?: string
        }
        Update: {
          id?: string
          playlist_id?: string
          video_id?: string
          position?: number
          created_at?: string
        }
      }
      watch_history: {
        Row: {
          id: string
          user_id: string
          video_id: string
          watched_at: string
          progress: number
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          watched_at?: string
          progress?: number
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          watched_at?: string
          progress?: number
        }
      }
      watch_later: {
        Row: {
          id: string
          user_id: string
          video_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          created_at?: string
        }
      }
      video_likes: {
        Row: {
          id: string
          user_id: string
          video_id: string
          is_like: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          is_like: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          is_like?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
