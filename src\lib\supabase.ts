import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      videos: {
        Row: {
          id: string
          title: string
          description: string | null
          video_url: string
          thumbnail_url: string | null
          duration: number | null
          views: number
          likes: number
          dislikes: number
          user_id: string
          category: string | null
          tags: string[] | null
          is_public: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          video_url: string
          thumbnail_url?: string | null
          duration?: number | null
          views?: number
          likes?: number
          dislikes?: number
          user_id: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          video_url?: string
          thumbnail_url?: string | null
          duration?: number | null
          views?: number
          likes?: number
          dislikes?: number
          user_id?: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string | null
          avatar_url: string | null
          bio: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          video_id: string
          user_id: string
          content: string
          likes: number
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          video_id: string
          user_id: string
          content: string
          likes?: number
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          user_id?: string
          content?: string
          likes?: number
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          subscriber_id: string
          channel_id: string
          created_at: string
        }
        Insert: {
          id?: string
          subscriber_id: string
          channel_id: string
          created_at?: string
        }
        Update: {
          id?: string
          subscriber_id?: string
          channel_id?: string
          created_at?: string
        }
      }
      playlists: {
        Row: {
          id: string
          title: string
          description: string | null
          user_id: string
          is_public: boolean
          thumbnail_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          user_id: string
          is_public?: boolean
          thumbnail_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          user_id?: string
          is_public?: boolean
          thumbnail_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      playlist_videos: {
        Row: {
          id: string
          playlist_id: string
          video_id: string
          position: number
          created_at: string
        }
        Insert: {
          id?: string
          playlist_id: string
          video_id: string
          position?: number
          created_at?: string
        }
        Update: {
          id?: string
          playlist_id?: string
          video_id?: string
          position?: number
          created_at?: string
        }
      }
      watch_history: {
        Row: {
          id: string
          user_id: string
          video_id: string
          watched_at: string
          progress: number
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          watched_at?: string
          progress?: number
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          watched_at?: string
          progress?: number
        }
      }
      watch_later: {
        Row: {
          id: string
          user_id: string
          video_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          created_at?: string
        }
      }
      video_likes: {
        Row: {
          id: string
          user_id: string
          video_id: string
          is_like: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          is_like: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          is_like?: boolean
          created_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: 'video_upload' | 'comment' | 'like' | 'subscription' | 'live_stream'
          title: string
          message: string
          action_url: string | null
          thumbnail_url: string | null
          from_user_id: string | null
          is_read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: 'video_upload' | 'comment' | 'like' | 'subscription' | 'live_stream'
          title: string
          message: string
          action_url?: string | null
          thumbnail_url?: string | null
          from_user_id?: string | null
          is_read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'video_upload' | 'comment' | 'like' | 'subscription' | 'live_stream'
          title?: string
          message?: string
          action_url?: string | null
          thumbnail_url?: string | null
          from_user_id?: string | null
          is_read?: boolean
          created_at?: string
        }
      }
      live_streams: {
        Row: {
          id: string
          title: string
          description: string | null
          stream_key: string
          is_live: boolean
          viewer_count: number
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          stream_key: string
          is_live?: boolean
          viewer_count?: number
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          stream_key?: string
          is_live?: boolean
          viewer_count?: number
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      community_posts: {
        Row: {
          id: string
          content: string
          type: 'text' | 'image' | 'video' | 'poll'
          media_url: string | null
          poll_options: string[] | null
          likes: number
          dislikes: number
          comments_count: number
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          content: string
          type: 'text' | 'image' | 'video' | 'poll'
          media_url?: string | null
          poll_options?: string[] | null
          likes?: number
          dislikes?: number
          comments_count?: number
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          content?: string
          type?: 'text' | 'image' | 'video' | 'poll'
          media_url?: string | null
          poll_options?: string[] | null
          likes?: number
          dislikes?: number
          comments_count?: number
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      community_comments: {
        Row: {
          id: string
          post_id: string
          user_id: string
          content: string
          likes: number
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          user_id: string
          content: string
          likes?: number
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          user_id?: string
          content?: string
          likes?: number
          created_at?: string
        }
      }
      video_chapters: {
        Row: {
          id: string
          video_id: string
          title: string
          start_time: number
          end_time: number | null
          thumbnail_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          video_id: string
          title: string
          start_time: number
          end_time?: number | null
          thumbnail_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          title?: string
          start_time?: number
          end_time?: number | null
          thumbnail_url?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
