# YTClone - Modern YouTube Clone

A highly scalable YouTube clone built with modern technologies including Next.js 14, TypeScript, Supabase, and shadcn/ui.

## 🚀 Features

- **Video Upload & Streaming**: Upload and stream videos with custom video player
- **User Authentication**: Secure authentication with Supa<PERSON> Auth
- **Real-time Comments**: Comment system with real-time updates
- **Video Management**: Like, view count, and video metadata
- **Responsive Design**: Modern UI with shadcn/ui components
- **Search Functionality**: Search videos by title
- **User Profiles**: User profiles with avatar support
- **Video Recommendations**: Related video suggestions

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui, Lucide React icons
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **State Management**: Zustand
- **Video Player**: Video.js
- **Styling**: Tailwind CSS with CSS variables
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone <your-repo-url>
cd ytaug
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Create the following tables in your Supabase database:

#### Profiles Table
```sql
create table profiles (
  id uuid references auth.users on delete cascade primary key,
  username text unique not null,
  full_name text,
  avatar_url text,
  bio text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS
alter table profiles enable row level security;

-- Policies
create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check (auth.uid() = id);

create policy "Users can update own profile." on profiles
  for update using (auth.uid() = id);
```

#### Videos Table
```sql
create table videos (
  id uuid default gen_random_uuid() primary key,
  title text not null,
  description text,
  video_url text not null,
  thumbnail_url text,
  duration integer,
  views integer default 0,
  likes integer default 0,
  dislikes integer default 0,
  user_id uuid references auth.users on delete cascade not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS
alter table videos enable row level security;

-- Policies
create policy "Videos are viewable by everyone." on videos
  for select using (true);

create policy "Users can insert their own videos." on videos
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own videos." on videos
  for update using (auth.uid() = user_id);

create policy "Users can delete their own videos." on videos
  for delete using (auth.uid() = user_id);
```

#### Comments Table
```sql
create table comments (
  id uuid default gen_random_uuid() primary key,
  video_id uuid references videos on delete cascade not null,
  user_id uuid references auth.users on delete cascade not null,
  content text not null,
  likes integer default 0,
  parent_id uuid references comments on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS
alter table comments enable row level security;

-- Policies
create policy "Comments are viewable by everyone." on comments
  for select using (true);

create policy "Users can insert their own comments." on comments
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own comments." on comments
  for update using (auth.uid() = user_id);

create policy "Users can delete their own comments." on comments
  for delete using (auth.uid() = user_id);
```

#### Storage Buckets
Create the following storage buckets in Supabase Storage:
- `videos` (for video files)
- `thumbnails` (for thumbnail images)

Make sure to set appropriate policies for public access.

#### Database Functions
```sql
-- Function to increment video views
create or replace function increment_views(video_id uuid)
returns void as $$
begin
  update videos set views = views + 1 where id = video_id;
end;
$$ language plpgsql;

-- Function to increment video likes
create or replace function increment_likes(video_id uuid)
returns void as $$
begin
  update videos set likes = likes + 1 where id = video_id;
end;
$$ language plpgsql;
```

### 4. Environment Variables

Copy `.env.example` to `.env.local` and fill in your Supabase credentials:

```bash
cp .env.example .env.local
```

Edit `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── upload/            # Video upload page
│   ├── watch/[id]/        # Video watch page
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components
│   ├── providers/         # Context providers
│   ├── ui/                # shadcn/ui components
│   └── video/             # Video-related components
├── lib/                   # Utility libraries
│   ├── supabase.ts        # Supabase client
│   └── utils.ts           # Utility functions
└── store/                 # Zustand stores
    ├── authStore.ts       # Authentication state
    └── videoStore.ts      # Video state
```

## 🎨 UI Components

This project uses [shadcn/ui](https://ui.shadcn.com/) for consistent, accessible UI components. The design system includes:

- Modern color palette with CSS variables
- Dark/light mode support
- Responsive design patterns
- Accessible components

## 🔧 Customization

### Adding New Features

1. **Database**: Add new tables/columns in Supabase
2. **Types**: Update TypeScript types in `src/lib/supabase.ts`
3. **Store**: Add new Zustand stores for state management
4. **Components**: Create new components following the existing patterns
5. **Pages**: Add new pages in the `src/app` directory

### Styling

- Modify `src/app/globals.css` for global styles
- Update `tailwind.config.ts` for Tailwind customizations
- Use CSS variables for consistent theming

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Environment Variables for Production

Make sure to set these in your deployment platform:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Supabase](https://supabase.com/) for the backend infrastructure
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Video.js](https://videojs.com/) for the video player
