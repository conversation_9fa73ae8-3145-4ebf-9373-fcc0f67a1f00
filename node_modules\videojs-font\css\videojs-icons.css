@font-face {
  font-family: VideoJS;
  src: url(data:application/font-woff;charset=utf-8;base64,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) format("woff");
  font-weight: normal;
  font-style: normal; }

.vjs-icon-play {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-play:before {
    content: "\f101"; }

.vjs-icon-play-circle {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-play-circle:before {
    content: "\f102"; }

.vjs-icon-pause {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-pause:before {
    content: "\f103"; }

.vjs-icon-volume-mute {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-volume-mute:before {
    content: "\f104"; }

.vjs-icon-volume-low {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-volume-low:before {
    content: "\f105"; }

.vjs-icon-volume-mid {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-volume-mid:before {
    content: "\f106"; }

.vjs-icon-volume-high {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-volume-high:before {
    content: "\f107"; }

.vjs-icon-fullscreen-enter {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-fullscreen-enter:before {
    content: "\f108"; }

.vjs-icon-fullscreen-exit {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-fullscreen-exit:before {
    content: "\f109"; }

.vjs-icon-spinner {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-spinner:before {
    content: "\f10a"; }

.vjs-icon-subtitles {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-subtitles:before {
    content: "\f10b"; }

.vjs-icon-captions {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-captions:before {
    content: "\f10c"; }

.vjs-icon-hd {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-hd:before {
    content: "\f10d"; }

.vjs-icon-chapters {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-chapters:before {
    content: "\f10e"; }

.vjs-icon-downloading {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-downloading:before {
    content: "\f10f"; }

.vjs-icon-file-download {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-file-download:before {
    content: "\f110"; }

.vjs-icon-file-download-done {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-file-download-done:before {
    content: "\f111"; }

.vjs-icon-file-download-off {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-file-download-off:before {
    content: "\f112"; }

.vjs-icon-share {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-share:before {
    content: "\f113"; }

.vjs-icon-cog {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-cog:before {
    content: "\f114"; }

.vjs-icon-square {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-square:before {
    content: "\f115"; }

.vjs-icon-circle {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-circle:before {
    content: "\f116"; }

.vjs-icon-circle-outline {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-circle-outline:before {
    content: "\f117"; }

.vjs-icon-circle-inner-circle {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-circle-inner-circle:before {
    content: "\f118"; }

.vjs-icon-cancel {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-cancel:before {
    content: "\f119"; }

.vjs-icon-repeat {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-repeat:before {
    content: "\f11a"; }

.vjs-icon-replay {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-replay:before {
    content: "\f11b"; }

.vjs-icon-replay-5 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-replay-5:before {
    content: "\f11c"; }

.vjs-icon-replay-10 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-replay-10:before {
    content: "\f11d"; }

.vjs-icon-replay-30 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-replay-30:before {
    content: "\f11e"; }

.vjs-icon-forward-5 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-forward-5:before {
    content: "\f11f"; }

.vjs-icon-forward-10 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-forward-10:before {
    content: "\f120"; }

.vjs-icon-forward-30 {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-forward-30:before {
    content: "\f121"; }

.vjs-icon-audio {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-audio:before {
    content: "\f122"; }

.vjs-icon-next-item {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-next-item:before {
    content: "\f123"; }

.vjs-icon-previous-item {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-previous-item:before {
    content: "\f124"; }

.vjs-icon-shuffle {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-shuffle:before {
    content: "\f125"; }

.vjs-icon-cast {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-cast:before {
    content: "\f126"; }

.vjs-icon-picture-in-picture-enter {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-picture-in-picture-enter:before {
    content: "\f127"; }

.vjs-icon-picture-in-picture-exit {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-picture-in-picture-exit:before {
    content: "\f128"; }

.vjs-icon-facebook {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-facebook:before {
    content: "\f129"; }

.vjs-icon-linkedin {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-linkedin:before {
    content: "\f12a"; }

.vjs-icon-twitter {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-twitter:before {
    content: "\f12b"; }

.vjs-icon-tumblr {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-tumblr:before {
    content: "\f12c"; }

.vjs-icon-pinterest {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-pinterest:before {
    content: "\f12d"; }

.vjs-icon-audio-description {
  font-family: VideoJS;
  font-weight: normal;
  font-style: normal; }
  .vjs-icon-audio-description:before {
    content: "\f12e"; }
